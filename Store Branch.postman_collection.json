{"info": {"_postman_id": "514806e8-a8b6-4167-b62b-9f50ad14e629", "name": "Store Branch", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "44493673", "_collection_link": "https://otlub-team-885924.postman.co/workspace/Otlub-Team~fbc2e0d7-dab7-4cfd-83cd-aae90a43f989/collection/40395180-514806e8-a8b6-4167-b62b-9f50ad14e629?action=share&source=collection_link&creator=44493673"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "email", "value": "<EMAIL>", "description": "<EMAIL> :<PERSON> | <EMAIL> :Live", "type": "text"}, {"key": "password", "value": "123456789", "description": "password :<PERSON> | 123456789 :Live", "type": "text"}]}, "url": {"raw": "{{auth_domain_store_branch}}login", "host": ["{{auth_domain_store_branch}}login"]}}, "response": []}, {"name": "Verify Otp", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "otp", "value": "1234", "type": "text"}]}, "url": {"raw": "{{auth_domain_store_branch}}verify-otp", "host": ["{{auth_domain_store_branch}}verify-otp"]}}, "response": []}, {"name": "Profile", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "otp", "value": "1234", "type": "text"}]}, "url": {"raw": "{{auth_domain_store_branch}}profile", "host": ["{{auth_domain_store_branch}}profile"]}}, "response": []}, {"name": "Resend Otp", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "email", "value": "<EMAIL>", "type": "text"}]}, "url": {"raw": "{{auth_domain_store_branch}}resend-otp", "host": ["{{auth_domain_store_branch}}resend-otp"]}}, "response": []}, {"name": "Logout", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "{{auth_domain_store_branch}}logout", "host": ["{{auth_domain_store_branch}}logout"]}}, "response": []}, {"name": "Logout All Devices", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "{{auth_domain_store_branch}}logout-all-devices", "host": ["{{auth_domain_store_branch}}logout-all-devices"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{auth_domain_store_branch}}login", "host": ["{{auth_domain_store_branch}}login"]}}, "response": []}]}, {"name": "Order", "item": [{"name": "Restaurant Filter Orders", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{domain_store_branch}}orders?stage=executing&pre_page=10&page", "host": ["{{domain_store_branch}}orders"], "query": [{"key": "stage", "value": "executing", "description": "pending,executing,ready,rejected"}, {"key": "pre_page", "value": "10", "description": "nullable, default: 10"}, {"key": "page", "value": null, "description": "nullable, you can get total pages from pagination.total returned object"}, {"key": "count", "value": "1", "disabled": true}, {"key": "offset", "value": "10", "disabled": true}, {"key": "status_result", "value": "", "disabled": true}]}}, "response": []}, {"name": "Filter General", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{domain_store_branch}}general-filter?type=compensation&start_date&end_date&per_page=5&page=1", "host": ["{{domain_store_branch}}general-filter"], "query": [{"key": "type", "value": "compensation", "description": "required (due, compensation) for bonds, (delivered, returned) for orders"}, {"key": "start_date", "value": null, "description": "nullable (21-11-2022)"}, {"key": "end_date", "value": null, "description": "nullable (21-11-2022)"}, {"key": "per_page", "value": "5", "description": "nullable: min=1 max=100"}, {"key": "page", "value": "1", "description": "nullable, Use pagination.total to find the total pages"}]}}, "response": []}, {"name": "Statistics", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{domain_store_branch}}statistics", "host": ["{{domain_store_branch}}statistics"]}}, "response": []}, {"name": "Search for order", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{domain_store_branch}}order-search?count=15&offset=1&code=2", "host": ["{{domain_store_branch}}order-search"], "query": [{"key": "count", "value": "15"}, {"key": "offset", "value": "1"}, {"key": "code", "value": "2"}]}}, "response": []}, {"name": "order-details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{domain_store_branch}}order/15", "host": ["{{domain_store_branch}}order"], "path": ["15"]}}, "response": []}, {"name": "update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "lang", "value": "ar", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"status\": \"ready_to_delivery\",\r\n    \"preparing_time\": 30\r\n}\r\n///// status should be one of these /////\r\n// pending\r\n// credit_card_check\r\n// cancelled\r\n// accepted\r\n// rejected\r\n// ready_to_take_away\r\n// ready_to_delivery\r\n// on_the_way\r\n// delivered\r\n// returned\r\n// executing\r\n// payment_rejected\r\n// scheduled\r\n// compensated\r\n// cash_paid", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain_store_branch}}order/15", "host": ["{{domain_store_branch}}order"], "path": ["15"]}}, "response": []}, {"name": "update branch status", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "lang", "value": "ar", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"status\": \"open\"\r\n}\r\n///// status should be one of these /////\r\n// open\r\n// close\r\n// busy", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain_store_branch}}store-branch-status", "host": ["{{domain_store_branch}}store-branch-status"]}}, "response": []}, {"name": "Update Order Status (from store branch)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"order_status\": \"delivered\", // accepted, executing, ready_to_take_away, ready_to_delivery, in_place, delivered\r\n    \"order_id\": 1156,\r\n    \"begin_executing_time\": \"2025-04-12T07:29:24.678Z\", // Required if and only if the order_status = accepted, 2025-04-12T07:29:24.678Z\r\n    \"end_executing_time\": \"2025-04-12T07:35:24.678Z\", // Required if and only if the order_status = accepted, 2025-04-12T07:35:24.678Z\r\n    \"executing_time\": \"6\" // Required if and only if the order_status = accepted, 6\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain_customer}}order/update-status", "host": ["{{domain_customer}}order"], "path": ["update-status"]}}, "response": []}]}, {"name": "Delivery Missions Order", "item": [{"name": "Update Delivery Mission Status (Delivery man app)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"delivery_mission_id\": 1,\r\n    \"status_before\": \"pending\", // pending,accepted,taken,on_the_way,delivered,cancelled\r\n    \"status_after\": \"accepted\" // pending,accepted,taken,on_the_way,delivered,cancelled\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{auth_domain_store_branch}}login", "host": ["{{auth_domain_store_branch}}login"]}}, "response": []}, {"name": "Store Delivery Mission", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"customer_name\": \"khaled\",\r\n    \"customer_mobile\": \"0599458453\",\r\n    \"customer_address\": \"شارع الشارع\",\r\n    \"customer_zone_id\": 14,\r\n    \"customer_neighborhood_id\": 2,\r\n    \"order_amount\": 130\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain_store_branch}}delivery-mission/store", "host": ["{{domain_store_branch}}delivery-mission"], "path": ["store"]}}, "response": []}, {"name": "Delivery Mission Fees", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain_store_branch}}delivery-mission/fees?customer_zone_id=14", "host": ["{{domain_store_branch}}delivery-mission"], "path": ["fees"], "query": [{"key": "customer_zone_id", "value": "14"}]}}, "response": []}]}, {"name": "Configs", "item": [{"name": "configs", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "api-password-b4p", "value": "kds@k2J#SF*4D98g!85w4", "type": "text"}, {"key": "lang", "value": "ar", "type": "text"}], "url": {"raw": "{{domain_store_branch}}configs", "host": ["{{domain_store_branch}}configs"]}}, "response": []}]}, {"name": "Banks", "item": [{"name": "Store", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "api-password-b4p", "value": "kds@k2J#SF*4D98g!85w4s", "type": "text"}, {"key": "lang", "value": "en", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"bank_id\": 1,\r\n    \"bank_branch_id\": 1,\r\n    \"person_name\": \"khaled <PERSON><PERSON>\",\r\n    \"account_number\": \"*********\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain_store_branch}}store-branch-bank", "host": ["{{domain_store_branch}}store-branch-bank"]}}, "response": []}, {"name": "Update", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "api-password-b4p", "value": "kds@k2J#SF*4D98g!85w4", "type": "text"}, {"key": "lang", "value": "en", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"bank_id\": 1,\r\n    \"bank_branch_id\": 1,\r\n    \"person_name\": \"k<PERSON><PERSON> updated\",\r\n    \"account_number\": \"*********\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain_store_branch}}store-branch-bank/1", "host": ["{{domain_store_branch}}store-branch-bank"], "path": ["1"]}}, "response": []}, {"name": "Show", "protocolProfileBehavior": {"disableBodyPruning": true, "disabledSystemHeaders": {}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "api-password-b4p", "value": "kds@k2J#SF*4D98g!85w4", "type": "text"}, {"key": "lang", "value": "en", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "{{domain_store_branch}}store-branch-bank/1", "host": ["{{domain_store_branch}}store-branch-bank"], "path": ["1"]}}, "response": []}, {"name": "Index", "protocolProfileBehavior": {"disableBodyPruning": true, "disabledSystemHeaders": {}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "api-password-b4p", "value": "kds@k2J#SF*4D98g!85w4", "type": "text"}, {"key": "lang", "value": "ar", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "{{domain_store_branch}}store-branch-bank", "host": ["{{domain_store_branch}}store-branch-bank"]}}, "response": []}, {"name": "Get Bank Branches", "protocolProfileBehavior": {"disableBodyPruning": true, "disabledSystemHeaders": {}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "api-password-b4p", "value": "kds@k2J#SF*4D98g!85w4", "type": "text"}, {"key": "lang", "value": "ar", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "{{domain_store_branch}}get-bank-branches/1", "host": ["{{domain_store_branch}}get-bank-branches"], "path": ["1"]}}, "response": []}, {"name": "Delete", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "api-password-b4p", "value": "kds@k2J#SF*4D98g!85w4", "type": "text"}, {"key": "lang", "value": "en", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "{{domain_store_branch}}store-branch-bank/5", "host": ["{{domain_store_branch}}store-branch-bank"], "path": ["5"]}}, "response": []}]}, {"name": "Financial", "item": [{"name": "Get Financial Information", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{domain_store_branch}}financial-summary-report?report_type=Daily&start_date=2025-04-20&end_date=2025-04-21", "host": ["{{domain_store_branch}}financial-summary-report"], "query": [{"key": "report_type", "value": "Daily", "description": "Daily,Weekly,Monthly,Yearly,Custom"}, {"key": "start_date", "value": "2025-04-20", "description": " Required: report_type=Custom 28/9/2000"}, {"key": "end_date", "value": "2025-04-21", "description": " Required: report_type=Custom 28/9/2000"}]}}, "response": []}]}, {"name": "store branch schedule", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "lang", "value": "ar", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"payload\": [\r\n        { \"day\": \"saturday\", \"is_active\": true, \"from\": \"09:00\", \"to\": \"17:00\" },\r\n        { \"day\": \"sunday\",   \"is_active\": false, \"from\": null,    \"to\": null },\r\n        { \"day\": \"monday\",   \"is_active\": true, \"from\": \"08:00\", \"to\": \"16:00\" },\r\n        { \"day\": \"tuesday\",  \"is_active\": true, \"from\": \"08:00\", \"to\": \"16:00\" },\r\n        { \"day\": \"wednesday\",\"is_active\": true, \"from\": \"08:00\", \"to\": \"16:00\" },\r\n        { \"day\": \"thursday\", \"is_active\": true, \"from\": \"08:00\", \"to\": \"16:00\" },\r\n        { \"day\": \"friday\",   \"is_active\": true, \"from\": \"08:00\", \"to\": \"16:00\" }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain_store_branch}}store-branch-schedule", "host": ["{{domain_store_branch}}store-branch-schedule"]}}, "response": []}, {"name": "Bond Request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "lang", "value": "ar", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"order_id\": 31,\r\n    \"order_price\": 50,\r\n    \"bond_note\": \"tet note\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain_store_branch}}bond-request/store", "host": ["{{domain_store_branch}}bond-request"], "path": ["store"]}}, "response": []}, {"name": "test", "request": {"method": "GET", "header": []}, "response": []}]}