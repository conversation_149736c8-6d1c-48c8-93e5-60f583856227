import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/Locale/translation.dart';
import 'package:provider/provider.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/providers/auth_provider.dart';
import 'package:hero_store_branch_app/providers/bank_info_provider.dart';
import 'package:hero_store_branch_app/providers/change_password_provider.dart';
import 'package:hero_store_branch_app/providers/config_provider.dart';
import 'package:hero_store_branch_app/view/screens/bank_accout/bank_account_info_screen.dart';
import 'package:hero_store_branch_app/view/screens/restaurant_details_screen/restaurant_details_screen.dart';
import 'package:hero_store_branch_app/view/screens/web_view_text_screen.dart';
import 'package:hero_store_branch_app/view/widgets/change_password_bottom_sheet.dart';
import 'package:hero_store_branch_app/view/widgets/custom_bottom_sheet.dart';
import 'package:hero_store_branch_app/view/widgets/custom_switch.dart';
import 'package:hero_store_branch_app/view/widgets/profile_app_bar.dart';
import 'package:hero_store_branch_app/view/widgets/settings_list_tile.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (_, authProvider, __) {
        return Column(
          children: [
            ProfileAppBar(
              email: authProvider.storeBranch.store.name,
              image: authProvider.storeBranch.store.storeLogo,
              name: authProvider.storeBranch.name,
            ),
            Expanded(
              child: ListView(
                children: [
                  SettingsListTile(
                    "clsoe_restaurant_temporary".tr,
                    leading: "assets/icons/logout_outline.png",
                    trailing: CustomSwitch(
                        active: authProvider.storeBranch.status.index > 0),
                    onTap: () => authProvider.toggleIsTemporaryClosed(),
                  ),
                  SettingsListTile(
                    "store_branch_details".tr,
                    leading: "assets/icons/paper_outline.png",
                    onTap: () => Navigator.of(context).push(MaterialPageRoute(
                        builder: (_) => RestaurantDetailsScreen())),
                  ),
                  SettingsListTile(
                    "change_password".tr,
                    leading: "assets/icons/key_outline.png",
                    onTap: () => showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      backgroundColor:
                          Color(0x669E9E9E), // Semi-transparent overlay
                      builder: (context) => ChangeNotifierProvider(
                        create: (context) => ChangePasswordProvider(),
                        child: ChangePasswordBottomSheet(),
                      ),
                    ),
                  ),
                  SettingsListTile(
                    "bank_account_info".tr,
                    leading: "assets/icons/wallet_outline.png",
                    onTap: () => Navigator.of(context).push(MaterialPageRoute(
                        builder: (_) =>
                            ChangeNotifierProvider<BankInfoProvider>(
                              create: (_) => BankInfoProvider(),
                              child: BankAccountInfoScreen(),
                            ))),
                  ),
                  SettingsListTile(
                    "change_language".tr,
                    leading: "assets/icons/switch_outline.png",
                    onTap: () => showModalBottomSheet(
                      context: context,
                      builder: (_) => CustomBottomSheet(children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 32),
                          child: Column(
                            children: [
                              Text(
                                "change_language".tr,
                                style: TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 16),
                              ),
                              ...dictionary.keys.map(
                                (e) => ListTile(
                                  dense: true,
                                  visualDensity: VisualDensity.compact,
                                  title: Text(e.tr),
                                  onTap: () => ConfigProvider().changeLocale(e),
                                ),
                              ),
                              SizedBox(height: 20),
                            ],
                          ),
                        ),
                      ]),
                    ),
                  ),
                  SettingsListTile(
                    "EULA".tr,
                    leading: "assets/icons/order_outline.png",
                    onTap: () => Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => WebViewTextScreen(
                          "EULA".tr,
                          link: "some link to be fetched",
                        ),
                      ),
                    ),
                  ),
                  SettingsListTile(
                    "privacy_policy".tr,
                    leading: "assets/icons/clock_outline.png",
                    onTap: () => Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => WebViewTextScreen(
                          "privacy_policy".tr,
                          link: "some link to be fetched",
                        ),
                      ),
                    ),
                  ),
                  SettingsListTile(
                    "logout".tr,
                    leading: "assets/icons/logout_outline.png",
                    onTap: () => AuthProvider().logout(),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
