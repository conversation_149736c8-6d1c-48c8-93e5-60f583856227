import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_kit/services/logger.dart';
import 'package:hero_store_branch_app/view/screens/reports_screens/all_reports_screen.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_logo.dart';
import 'package:hero_store_branch_app/view/widgets/one_side_card.dart';
import 'package:hero_store_branch_app/view/widgets/report_tile.dart';
import 'package:hero_store_branch_app/providers/finance_provider.dart';
import 'package:provider/provider.dart';

class FinancePage extends StatefulWidget {
  const FinancePage({super.key});

  @override
  State<FinancePage> createState() => _FinancePageState();
}

class _FinancePageState extends State<FinancePage> {
  @override
  void initState() {
    super.initState();
    // Load financial data when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<FinanceProvider>().loadFinancialSummary();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<FinanceProvider>(
      builder: (context, financeProvider, child) {
        final TextStyle labelTextStyle =
            TextStyle(fontSize: 13, color: Colors.grey[700]);
        final OutlineInputBorder outlinedInputBorder = OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: Colors.grey[300]!,
            width: .5,
          ),
        );
        late final InputDecorationTheme inputDecorationTheme =
            InputDecorationTheme(
          isDense: true,
          contentPadding: EdgeInsets.symmetric(vertical: 0, horizontal: 8),
          constraints: BoxConstraints.tight(
            Size(
              double.maxFinite,
              42,
            ),
          ),
          border: outlinedInputBorder,
          enabledBorder: outlinedInputBorder,
          focusedBorder: outlinedInputBorder,
          focusedErrorBorder: outlinedInputBorder,
          hintStyle: labelStyle.copyWith(color: Colors.grey[500]),
        );

        final financialData = financeProvider.financialSummary;

        return Scaffold(
          body: Column(
            children: [
              AppBarWithLogo(),
              Container(
                color: lightCardColor,
                height: 86,
                padding: EdgeInsets.symmetric(
                  vertical: 10,
                  horizontal: 16,
                ),
                margin: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(100),
                        color: primaryColor.light,
                      ),
                      child: Image.asset(
                        "assets/icons/wallet_outline.png",
                        color: primaryColor,
                        scale: 2,
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "total_price".tr,
                              style: TextStyle(fontWeight: FontWeight.w500),
                            ),
                            Text(
                              financialData?.totalAmount ?? "0 $currency",
                              style: TextStyle(
                                color: primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Material(
                      color: primaryColor.light,
                      borderRadius: BorderRadius.circular(100),
                      clipBehavior: Clip.hardEdge,
                      child: InkWell(
                        onTap: () {
                          if (financialData?.invoicePdfUrl != null &&
                              financialData!.invoicePdfUrl.isNotEmpty) {
                            Log("Opening PDF: ${financialData.invoicePdfUrl}");
                            // TODO: Add url_launcher package and implement PDF opening
                          } else {
                            Log("Print...");
                          }
                        },
                        child: Ink(
                          padding: EdgeInsets.all(8),
                          child: Image.asset(
                            "assets/icons/print_filled.png",
                            color: primaryColor.dark,
                            scale: 2,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SizedBox(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(),
                      Text(
                        "filter".tr,
                        style: TextStyle(
                            fontWeight: FontWeight.w500, fontSize: 13),
                      ),
                      SizedBox(height: 8),
                      DropdownMenu<String>(
                        textStyle: labelTextStyle,
                        width: MediaQuery.of(context).size.width - 32,
                        initialSelection: financeProvider.selectedReportType,
                        trailingIcon: Icon(Icons.keyboard_arrow_down_rounded),
                        selectedTrailingIcon:
                            Icon(Icons.keyboard_arrow_down_rounded),
                        inputDecorationTheme: inputDecorationTheme,
                        onSelected: (String? value) {
                          if (value != null) {
                            financeProvider.setReportType(value);
                          }
                        },
                        dropdownMenuEntries: financeProvider.reportTypes
                            .map((reportType) => DropdownMenuEntry<String>(
                                  style: ButtonStyle(
                                    textStyle:
                                        WidgetStateProperty.all(labelTextStyle),
                                  ),
                                  value: reportType,
                                  label: reportType == 'Daily'
                                      ? "daily_report".tr
                                      : reportType,
                                ))
                            .toList(),
                      ),
                      // Show date inputs when Custom is selected
                      if (financeProvider.selectedReportType == 'Custom') ...[
                        SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "start_date".tr,
                                    style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontSize: 13),
                                  ),
                                  SizedBox(height: 4),
                                  GestureDetector(
                                    onTap: () => _selectStartDate(
                                        context, financeProvider),
                                    child: Container(
                                      height: 42,
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 8),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: Colors.grey[300]!,
                                          width: 0.5,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              _formatDisplayDate(
                                                  financeProvider.startDate),
                                              style: labelTextStyle,
                                            ),
                                          ),
                                          Icon(
                                            Icons.calendar_today,
                                            size: 16,
                                            color: Colors.grey[600],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "end_date".tr,
                                    style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontSize: 13),
                                  ),
                                  SizedBox(height: 4),
                                  GestureDetector(
                                    onTap: () => _selectEndDate(
                                        context, financeProvider),
                                    child: Container(
                                      height: 42,
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 8),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: Colors.grey[300]!,
                                          width: 0.5,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              _formatDisplayDate(
                                                  financeProvider.endDate),
                                              style: labelTextStyle,
                                            ),
                                          ),
                                          Icon(
                                            Icons.calendar_today,
                                            size: 16,
                                            color: Colors.grey[600],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              SizedBox(height: 8),
              if (financeProvider.isLoading)
                Expanded(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                )
              else if (financeProvider.error != null)
                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          financeProvider.error!,
                          style: TextStyle(color: Colors.red),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => financeProvider.refresh(),
                          child: Text("Retry"),
                        ),
                      ],
                    ),
                  ),
                )
              else
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            OneSideCard(
                                label: "cash".tr,
                                value: _extractValueFromCurrency(
                                    financialData?.cash ?? "0"),
                                top: true,
                                image: "assets/icons/dollar_outline.png"),
                            Spacer(flex: 1),
                            OneSideCard(
                                label: "digital".tr,
                                value: _extractValueFromCurrency(
                                    financialData?.creditCard ?? "0"),
                                top: true,
                                image: "assets/icons/visa_outline.png"),
                          ],
                        ),
                        SizedBox(height: 6),
                        Row(
                          children: [
                            OneSideCard(
                                label: "orders_sum".tr,
                                value: financialData?.ordersNumber ?? "0",
                                top: false,
                                image: "assets/icons/order_outline.png"),
                            Spacer(flex: 1),
                            OneSideCard(
                                label: "discounts_and_coupons".tr,
                                value: _extractValueFromCurrency(
                                    financialData?.discountedAmount ?? "0"),
                                top: false,
                                image: "assets/icons/discount_outline.png"),
                          ],
                        ),
                        SizedBox(height: 6),
                        Row(
                          children: [
                            Text(
                              "report_list".tr,
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Spacer(),
                            TextButton(
                              onPressed: () => Navigator.of(context).push(
                                  MaterialPageRoute(
                                      builder: (_) => AllReportsScreen())),
                              child: Text("show_all".tr),
                            )
                          ],
                        ),
                        if (financialData?.orders.isNotEmpty == true)
                          ...financialData!.orders.map(
                            (order) => ReportTile(
                              title: order.title,
                              subtitle: order.subtitle,
                              image: order.image,
                            ),
                          )
                        else
                          Padding(
                            padding: EdgeInsets.all(32),
                            child: Text(
                              "No orders found",
                              style: TextStyle(color: Colors.grey),
                              textAlign: TextAlign.center,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  String _extractValueFromCurrency(String currencyValue) {
    // Extract numeric value from currency string like "4567₪" -> "4567"
    // Handle different currency symbols and formats
    String cleanValue = currencyValue.replaceAll(RegExp(r'[^\d.,]'), '');
    // If empty after cleaning, return "0"
    return cleanValue.isEmpty ? "0" : cleanValue;
  }

  Future<void> _selectStartDate(
      BuildContext context, FinanceProvider provider) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: provider.startDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      helpText: "Select Start Date",
    );
    if (picked != null && picked != provider.startDate) {
      // Ensure start date is not after end date
      DateTime endDate = provider.endDate;
      if (picked.isAfter(endDate)) {
        endDate = picked;
      }
      provider.setDateRange(picked, endDate);
    }
  }

  Future<void> _selectEndDate(
      BuildContext context, FinanceProvider provider) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: provider.endDate,
      firstDate: provider.startDate,
      lastDate: DateTime.now(),
      helpText: "Select End Date",
    );
    if (picked != null && picked != provider.endDate) {
      provider.setDateRange(provider.startDate, picked);
    }
  }

  String _formatDisplayDate(DateTime date) {
    return "${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}";
  }
}
