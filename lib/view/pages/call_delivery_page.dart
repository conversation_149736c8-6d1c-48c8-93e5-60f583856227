import 'package:flutter/material.dart';
import 'package:hero_kit/models/neighborhood.dart';
import 'package:hero_kit/models/zone.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/data/delivery_mission_repo.dart';
import 'package:hero_store_branch_app/providers/config_provider.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_title.dart';
import 'package:hero_store_branch_app/view/widgets/label_with_padding.dart';

class CallDeliveryPage extends StatefulWidget {
  const CallDeliveryPage({super.key});

  @override
  State<CallDeliveryPage> createState() => _CallDeliveryPageState();
}

class _CallDeliveryPageState extends State<CallDeliveryPage> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController zoneController = TextEditingController();
  final TextEditingController neighborhoodController = TextEditingController();
  final TextEditingController deliveryChargeController = TextEditingController();
  final TextEditingController orderPriceController = TextEditingController();
  final TextEditingController noteController = TextEditingController();
  final OutlineInputBorder outlinedInputBorder = OutlineInputBorder(
    borderRadius: BorderRadius.circular(8),
    borderSide: BorderSide(
      color: Colors.grey[300]!,
      width: .5,
    ),
  );
  late final InputDecoration inputDecoration = InputDecoration(
    isDense: true,
    contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 10),
    border: outlinedInputBorder,
    enabledBorder: outlinedInputBorder,
    focusedBorder: outlinedInputBorder,
    focusedErrorBorder: outlinedInputBorder,
    hintStyle: labelStyle.copyWith(color: Colors.grey[500]),
  );

  final List<String> preparingList = [
    "10 - 15",
    "15 - 20",
    "20 - 25",
    "25 - 30",
    "30 - 35",
  ];

  late String preparingTime = preparingList.first;
  Zone? selectedZone;
  Neighborhood? selectedNeighborhood;
  bool canSelectNeighborhood = true;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppBarWithTitle("call_delivery".tr),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(
              vertical: 16,
              horizontal: 16,
            ),
            child: Form(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(),
                  LabelWithPadding("customer_name".tr),
                  TextFormField(
                    controller: nameController,
                    textInputAction: TextInputAction.next,
                    style: labelStyle,
                    decoration: inputDecoration.copyWith(
                      hintText: "name".tr,
                    ),
                    keyboardType: TextInputType.name,
                  ),
                  LabelWithPadding("phone_number".tr),
                  TextFormField(
                    controller: phoneController,
                    textInputAction: TextInputAction.next,
                    style: labelStyle,
                    decoration: inputDecoration.copyWith(
                      hintText: "phone_number".tr,
                    ),
                    keyboardType: TextInputType.phone,
                  ),
                  LabelWithPadding("zone".tr),
                  TextFormField(
                    enableInteractiveSelection: false,
                    controller: zoneController,
                    textInputAction: TextInputAction.next,
                    style: labelStyle,
                    decoration: inputDecoration.copyWith(
                      hintText: "zone".tr,
                    ),
                    keyboardType: TextInputType.streetAddress,
                    // enabled: false,
                    canRequestFocus: false,
                    onTap: () async {
                      Zone? zone = await selectZone(context);
                      if (zone == null || !context.mounted) {
                        return;
                      }

                      Iterable<Neighborhood> availableNeighborhoods =
                          ConfigProvider().config.neighborhoods.where((n) => n.zoneId == zone.id);
                      canSelectNeighborhood = availableNeighborhoods.isNotEmpty;

                      setState(() {
                        selectedNeighborhood = null;
                        neighborhoodController.text = "";
                        selectedZone = zone;
                        zoneController.text = zone.name;
                      });
                      fetchDeliveryCharge();
                    },
                  ),
                  if (canSelectNeighborhood) LabelWithPadding("neighborhood".tr),
                  if (canSelectNeighborhood)
                    TextFormField(
                      canRequestFocus: false,
                      enableInteractiveSelection: false,
                      controller: neighborhoodController,
                      textInputAction: TextInputAction.next,
                      style: labelStyle,
                      decoration: inputDecoration.copyWith(
                        hintText: "neighborhood".tr,
                      ),
                      keyboardType: TextInputType.streetAddress,
                      onTap: () async {
                        if (selectedZone == null) {
                          alertSelectCityFirst(context);
                          return;
                        }
                        Neighborhood? neighborhood = await selectNeighborhood(context);
                        if (neighborhood == null || !context.mounted) {
                          return;
                        }
                        setState(() {
                          neighborhoodController.text = neighborhood.name;
                          selectedNeighborhood = neighborhood;
                        });
                      },
                    ),
                  LabelWithPadding("delivery_charge".tr),
                  TextFormField(
                    enabled: false,
                    canRequestFocus: false,
                    enableInteractiveSelection: false,
                    keyboardType: TextInputType.number,
                    controller: deliveryChargeController,
                    textInputAction: TextInputAction.next,
                    style: labelStyle,
                    decoration: inputDecoration.copyWith(
                      hintText: "delivery_charge".tr,
                      suffixIcon: SizedBox(
                        child: Center(
                          child: Text(currency),
                        ),
                      ),
                      suffixIconConstraints: BoxConstraints(
                        maxHeight: 32,
                        maxWidth: 32,
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    ),
                  ),
                  LabelWithPadding("order_price".tr),
                  TextFormField(
                    keyboardType: TextInputType.number,
                    controller: orderPriceController,
                    contextMenuBuilder: (context, editableTextState) => SizedBox(),
                    textInputAction: TextInputAction.next,
                    style: labelStyle,
                    decoration: inputDecoration.copyWith(
                      hintText: "order_price".tr,
                      suffixIcon: SizedBox(
                        child: Center(
                          child: Text(currency),
                        ),
                      ),
                      suffixIconConstraints: BoxConstraints(
                        maxHeight: 32,
                        maxWidth: 32,
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    ),
                  ),
                  LabelWithPadding("note".tr),
                  TextFormField(
                    controller: noteController,
                    textInputAction: TextInputAction.next,
                    style: labelStyle, maxLines: 3,
                    decoration: inputDecoration.copyWith(
                      hintText: "delivery_man_note".tr,
                    ),
                    keyboardType: TextInputType.multiline,
                    // enabled: false,
                    // canRequestFocus: false,
                  ),
                  LabelWithPadding("preparing_time".tr),
                  SizedBox(
                    // height: 400,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: List.generate(
                        (preparingList.length / 2).ceil(),
                        (i) => Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Spacer(),
                            DenseToggleButton(
                              "${preparingList[i * 2]} ${"min".tr}",
                              active: preparingTime == preparingList[i * 2],
                              onTap: () => setState(() {
                                preparingTime = preparingList[i * 2];
                              }),
                            ),
                            preparingList.length > i * 2 + 1 ? Spacer() : SizedBox(),
                            preparingList.length > i * 2 + 1
                                ? DenseToggleButton(
                                    "${preparingList[i * 2 + 1]} ${"min".tr}",
                                    active: preparingTime == preparingList[i * 2 + 1],
                                    onTap: () => setState(() {
                                      preparingTime = preparingList[i * 2 + 1];
                                    }),
                                  )
                                : SizedBox(),
                            Spacer(),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    child: Padding(
                      padding: EdgeInsets.only(top: 16),
                      child: ElevatedButton(
                        style: elevatedButtonStyle,
                        onPressed: () => callDelivery(),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text("call_delivery".tr),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<Zone?> selectZone(BuildContext context) {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        content: SizedBox(
          width: MediaQuery.of(context).size.width / 2,
          height: MediaQuery.of(context).size.height / 2,
          child: ListView(
            children: ConfigProvider()
                .config
                .zones
                .map(
                  (e) => ListTile(
                    title: Text(e.name),
                    dense: true,
                    onTap: () => Navigator.of(context).pop(e),
                  ),
                )
                .toList(),
          ),
        ),
      ),
    );
  }

  Future<Neighborhood?> selectNeighborhood(BuildContext context) {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        content: SizedBox(
          width: MediaQuery.of(context).size.width / 2,
          height: MediaQuery.of(context).size.height / 2,
          child: ListView(
            children: ConfigProvider()
                .config
                .neighborhoods
                .where((n) => n.zoneId == selectedZone?.id)
                .map((e) => ListTile(
                      title: Text(e.name),
                      dense: true,
                      onTap: () => Navigator.of(context).pop(e),
                    ))
                .toList(),
          ),
        ),
      ),
    );
  }

  void alertSelectCityFirst(BuildContext context) => showDialog(
        context: context,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(width: double.maxFinite),
              Text("data"),
              TextButton(onPressed: () => Navigator.of(context).pop(), child: Text("ok".tr)),
            ],
          ),
        ),
      );

  Future<void> fetchDeliveryCharge() async {
    Map<String, dynamic> result = await DeliveryMissionRepo().fetchDeliveryFee(selectedZone!.id);
    if (!result["result"]) {
      return;
    }
    deliveryChargeController.text = result["body"]["data"]["delivery_fees"];
    setState(() {});
  }

  Future<void> callDelivery() async {
    if (nameController.text.isEmpty) {
      showMessageDialog("add_customer_name".tr);
      return;
    }
    if (phoneController.text.isEmpty) {
      showMessageDialog("add_customer_phone".tr);
      return;
    }
    if (selectedZone == null) {
      showMessageDialog("select_customer_zone".tr);
      return;
    }

    if (orderPriceController.text.isEmpty) {
      showMessageDialog("add_order_amount".tr);
      return;
    }
    int? parsedAmount = int.tryParse(orderPriceController.text);
    if (parsedAmount == null) {
      showMessageDialog("invalid_order_amount".tr);
      return;
    }

    if (noteController.text.isEmpty) {
      showMessageDialog("add_address_details".tr);
      return;
    }

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (_) => Center(
        child: CircularProgressIndicator(),
      ),
    );
    Map<String, dynamic> result = await DeliveryMissionRepo().storeDeliveryMission(
      customerName: nameController.text,
      address: noteController.text,
      mobile: phoneController.text,
      zoneId: selectedZone!.id,
      orderAmount: int.parse(orderPriceController.text),
      preparingTime: preparingTime,
      neighborhoodId: selectedNeighborhood?.id,
    );
    if (!context.mounted) {
      return;
    }
    Navigator.of(context).pop();
    showMessageDialog(result["body"]["message"]);
  }

  void showMessageDialog(String message) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: whiteColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        content: Text(message),
        actionsAlignment: MainAxisAlignment.center,
        actions: [
          ElevatedButton(
            style: elevatedButtonStyle,
            onPressed: () => Navigator.pop(context),
            child: Text("ok".tr),
          ),
        ],
      ),
    );
  }
}

class DenseToggleButton extends StatelessWidget {
  final String text;
  final bool active;
  final void Function()? onTap;
  const DenseToggleButton(
    this.text, {
    this.onTap,
    required this.active,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Material(
        color: active ? primaryColor : Colors.grey[300],
        clipBehavior: Clip.hardEdge,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          onTap: onTap,
          child: Ink(
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 24),
            child: Text(
              text,
              style: TextStyle(
                  color: active ? Colors.white : primaryColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500),
            ),
          ),
        ),
      ),
    );
  }
}
