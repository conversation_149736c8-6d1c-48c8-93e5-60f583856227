import 'package:flutter/material.dart';
import 'package:hero_kit/enums/order_filter.dart';
import 'package:hero_kit/enums/order_status.dart';
import 'package:hero_store_branch_app/view/widgets/custom_date_pickers.dart';
import 'package:provider/provider.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/providers/orders_provider.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_logo.dart';
import 'package:hero_store_branch_app/view/widgets/custom_toggle_button.dart';
import 'package:hero_store_branch_app/view/widgets/order_card.dart';

class CompleteOrderPage extends StatelessWidget {
  const CompleteOrderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppBarWithLogo(),
        SizedBox(height: 12),
        Selector<OrdersProvider, OrderFilter>(
          selector: (_, orderProvider) => orderProvider.selectedOrderFilter,
          builder: (_, selectedOrderFilter, __) => SizedBox(
            height: 36,
            child: Row(
              children: [
                CustomToggleButton(
                  "delivered".tr,
                  active: selectedOrderFilter == OrderFilter.delivered,
                  fontSize: 11,
                  onTap: () => Provider.of<OrdersProvider>(context, listen: false)
                      .selectOrderFilter(OrderFilter.delivered),
                ),
                CustomToggleButton(
                  "returned".tr,
                  active: selectedOrderFilter == OrderFilter.returned,
                  fontSize: 11,
                  onTap: () => Provider.of<OrdersProvider>(context, listen: false)
                      .selectOrderFilter(OrderFilter.returned),
                ),
                CustomToggleButton(
                  "due".tr,
                  active: selectedOrderFilter == OrderFilter.due,
                  fontSize: 11,
                  onTap: () => Provider.of<OrdersProvider>(context, listen: false)
                      .selectOrderFilter(OrderFilter.due),
                ),
                CustomToggleButton(
                  "compensation".tr,
                  active: selectedOrderFilter == OrderFilter.compensated,
                  fontSize: 11,
                  onTap: () => Provider.of<OrdersProvider>(context, listen: false)
                      .selectOrderFilter(OrderFilter.compensated),
                ),
              ],
            ),
          ),
        ),
        SizedBox(height: 8),
        Selector<OrdersProvider, String>(
          selector: (_, orderProvider) => orderProvider.selectedCompleteOrderPeriod,
          builder: (_, completeOrderPeriod, __) => SizedBox(
            height: 36,
            child: Row(
              children: [
                "daily",
                "weekly",
                "monthly",
                "annual",
                "custom",
              ]
                  .map(
                    (e) => CustomToggleButton(
                      e.tr,
                      active: completeOrderPeriod == e,
                      fontSize: 11,
                      onTap: () => Provider.of<OrdersProvider>(context, listen: false)
                          .selectCompleteOrderPeriod(e),
                    ),
                  )
                  .toList(),
            ),
          ),
        ),
        SizedBox(height: 8),
        //datepickers start

        //daily
        Selector<OrdersProvider, String>(
          selector: (_, orderProvider) => orderProvider.selectedCompleteOrderPeriod,
          builder: (_, selectedCompleteOrderPeriod, __) => selectedCompleteOrderPeriod == "daily"
              ? Selector<OrdersProvider, DateTime?>(
                  selector: (_, orderProvider) => orderProvider.periodFilterStartDate,
                  builder: (_, startDate, __) => CustomDatePicker(
                    expanded: false,
                    dateTime: startDate,
                    label: "day".tr,
                    minDate: DateTime.fromMillisecondsSinceEpoch(0),
                    maxDate: DateTime.now(),
                    onPickedNewDate: (newDateTime) =>
                        Provider.of<OrdersProvider>(context, listen: false)
                            .setStartDate(newDateTime),
                  ),
                )
              : SizedBox(),
        ),

        //weekly
        Selector<OrdersProvider, String>(
          selector: (_, orderProvider) => orderProvider.selectedCompleteOrderPeriod,
          builder: (_, selectedCompleteOrderPeriod, __) => selectedCompleteOrderPeriod == "weekly"
              ? Selector<OrdersProvider, DateTime?>(
                  selector: (_, orderProvider) => orderProvider.periodFilterStartDate,
                  builder: (_, startDate, __) => CustomDurationPicker(
                    dateTime: startDate,
                    label: "week".tr,
                    onPickedNewDate: (newDateTime) =>
                        Provider.of<OrdersProvider>(context, listen: false)
                            .setStartDate(newDateTime),
                  ),
                )
              : SizedBox(),
        ),

        //monthly
        Selector<OrdersProvider, String>(
          selector: (_, orderProvider) => orderProvider.selectedCompleteOrderPeriod,
          builder: (_, selectedCompleteOrderPeriod, __) => selectedCompleteOrderPeriod == "monthly"
              ? Selector<OrdersProvider, DateTime?>(
                  selector: (_, orderProvider) => orderProvider.periodFilterStartDate,
                  builder: (_, startDate, __) => CustomMonthPicker(
                    dateTime: startDate,
                    label: "month".tr,
                    onPickedNewDate: (newDateTime) =>
                        Provider.of<OrdersProvider>(context, listen: false)
                            .setStartDate(newDateTime),
                  ),
                )
              : SizedBox(),
        ),

        //annual
        Selector<OrdersProvider, String>(
          selector: (_, orderProvider) => orderProvider.selectedCompleteOrderPeriod,
          builder: (_, selectedCompleteOrderPeriod, __) => selectedCompleteOrderPeriod == "annual"
              ? Selector<OrdersProvider, DateTime?>(
                  selector: (_, orderProvider) => orderProvider.periodFilterStartDate,
                  builder: (_, startDate, __) => CustomYearPicker(
                    dateTime: startDate,
                    label: "year".tr,
                    onPickedNewDate: (newDateTime) =>
                        Provider.of<OrdersProvider>(context, listen: false)
                            .setStartDate(newDateTime),
                  ),
                )
              : SizedBox(),
        ),

        //custom
        Selector<OrdersProvider, String>(
          selector: (_, orderProvider) => orderProvider.selectedCompleteOrderPeriod,
          builder: (_, selectedCompleteOrderPeriod, __) => selectedCompleteOrderPeriod == "custom"
              ? Selector<OrdersProvider, DateTime?>(
                  selector: (_, orderProvider) => orderProvider.periodFilterStartDate,
                  builder: (_, startDate, __) => Row(
                    children: [
                      CustomDatePicker(
                        dateTime: startDate,
                        label: "from".tr,
                        minDate: DateTime.fromMillisecondsSinceEpoch(0),
                        maxDate: DateTime.now(),
                        onPickedNewDate: (newDateTime) =>
                            Provider.of<OrdersProvider>(context, listen: false)
                                .setStartDate(newDateTime),
                      ),
                      Selector<OrdersProvider, DateTime?>(
                          selector: (_, orderProvider) => orderProvider.periodFilterEndDate,
                          builder: (_, endDate, __) {
                            return CustomDatePicker(
                              label: "to".tr,
                              dateTime: endDate,
                              minDate: startDate ?? DateTime.fromMillisecondsSinceEpoch(0),
                              maxDate: DateTime.now(),
                              onPickedNewDate: (newDateTime) =>
                                  Provider.of<OrdersProvider>(context, listen: false)
                                      .setEndDate(newDateTime),
                            );
                          }),
                    ],
                  ),
                )
              : SizedBox(),
        ),

        //datepickers end
        SizedBox(height: 8),
        Selector<OrdersProvider, OrderFilter>(
          selector: (_, orderProvider) => orderProvider.selectedOrderFilter,
          builder: (_, selectedOrderFilter, __) => Expanded(
            child: ListView(
              padding: EdgeInsets.all(0),
              physics: ClampingScrollPhysics(),
              children: switch (selectedOrderFilter) {
                OrderFilter.delivered => Provider.of<OrdersProvider>(context, listen: false)
                    .completeOrderList
                    .where((o) => o.orderStatus == OrderStatus.delivered)
                    .map((order) => OrderCard(order: order))
                    .toList(),
                OrderFilter.returned => Provider.of<OrdersProvider>(context, listen: false)
                    .completeOrderList
                    .where((o) => o.orderStatus == OrderStatus.returned)
                    .map((order) => OrderCard(order: order))
                    .toList(),
                OrderFilter.due => Provider.of<OrdersProvider>(context, listen: false)
                    .completeBondList
                    .where((o) => o.isDue)
                    .map((order) => Text("bond card"))
                    .toList(),
                OrderFilter.compensated => Provider.of<OrdersProvider>(context, listen: false)
                    .completeBondList
                    .where((o) => !o.isDue)
                    .map((order) => Text("bond card"))
                    .toList(),
              },
            ),
          ),
        ),
      ],
    );
  }
}
