import 'package:flutter/material.dart';
import 'package:hero_kit/models/delivery_order.dart';
import 'package:hero_store_branch_app/view/widgets/pagination_widget.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/providers/orders_provider.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_logo.dart';
import 'package:hero_store_branch_app/view/widgets/order_card.dart';
import 'package:hero_store_branch_app/view/widgets/orderlist_filter_card.dart';

class MainPage extends StatelessWidget {
  const MainPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppBarWithLogo(),
        SizedBox(
          height: 70,
          child: Selector<OrdersProvider, String>(
            selector: (_, orderProvider) => orderProvider.selectedStatusFilter,
            builder: (context, selectedStatusFilter, widget) => SizedBox(
              height: 70,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <String>["pending", "executing", "ready", "rejected"]
                    .map(
                      (s) => Selector<OrdersProvider, Map<String, int>>(
                        selector: (_, orderProvider) => orderProvider.orderCounters,
                        builder: (_, orderCounters, __) => OrderlistFilterCard(
                          count: orderCounters["${s}_count"] ?? 0,
                          text: s,
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
          ),
        ),
        Selector<OrdersProvider, List<DeliveryOrder>>(
          selector: (_, orderProvider) => orderProvider.runningOrderList,
          builder: (context, runningOrderList, child) => Expanded(
            child: ListView(
              padding: EdgeInsets.all(0),
              physics: ClampingScrollPhysics(),
              children: [
                ...runningOrderList.map(
                  (order) => OrderCard(order: order),
                ),
                Selector<OrdersProvider, int>(
                    selector: (_, op) => op.runningOrderPage,
                    builder: (_, page, __) {
                      if (runningOrderList.length == 10 || page != 1) {
                        return PaginationWidget(
                          page: page,
                          onBack: page == 1
                              ? null
                              : () => Provider.of<OrdersProvider>(context, listen: false)
                                  .runningOrdersBack(),
                          onNext: runningOrderList.length < 10
                              ? null
                              : () => Provider.of<OrdersProvider>(context, listen: false)
                                  .runningOrdersNext(),
                        );
                      }
                      return SizedBox();
                    }),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
