import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:hero_kit/models/delivery_order.dart';
import 'package:intl/intl.dart' as intl;
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/enums/order_status.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/providers/orders_provider.dart';
import 'package:hero_store_branch_app/view/screens/order_details_screen/order_details_screen.dart';
import 'package:hero_store_branch_app/view/widgets/order_tag.dart';
import 'package:hero_store_branch_app/view/widgets/order_timer.dart';

class OrderCard extends StatelessWidget {
  final DeliveryOrder order;
  const OrderCard({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(10),
      child: Material(
        color: backgroundText,
        clipBehavior: Clip.hardEdge,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          onTap: () {
            Provider.of<OrdersProvider>(context, listen: false).getOrderDetails(order.id);
            Navigator.of(context)
                .push(MaterialPageRoute(builder: (_) => OrderDetailsScreen()))
                .then((_) {
              if (context.mounted) {
                Provider.of<OrdersProvider>(context, listen: false).clearOrderDetails();
              }
            });
          },
          child: Ink(
            padding: EdgeInsets.symmetric(horizontal: 12),
            height: 90,
            child: Column(
              children: [
                Flexible(
                  flex: 1,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            intl.DateFormat("dd/MM/yyyy - hh:mma").format(order.createdAt),
                            textDirection: TextDirection.ltr,
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                              color: primaryColor,
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                "${"order_number".tr}: ",
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                ),
                              ),
                              Text(
                                "#${order.orderNumber}",
                                style: TextStyle(
                                  fontSize: 11,
                                  color: primaryColor,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(6),
                                  color: secondaryColor.lightActive,
                                ),
                                child: Text(
                                  "${order.orderAmount} $currency",
                                  style: TextStyle(
                                    color: secondaryColor,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              SizedBox(width: 8),
                              Material(
                                borderRadius: BorderRadius.circular(32),
                                clipBehavior: Clip.hardEdge,
                                color: Colors.transparent,
                                child: InkWell(
                                  onTap: () => log("Print Order No. ${order.id}"),
                                  child: Ink(
                                    padding: EdgeInsets.all(5),
                                    width: 38,
                                    height: 38,
                                    child: Image.asset(
                                      "assets/icons/print_filled.png",
                                      scale: 2,
                                      color: primaryColor,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Flexible(
                  flex: 1,
                  child: Row(
                    children: [
                      OrderTag(text: order.orderType.name.tr),
                      SizedBox(width: 8),
                      OrderTag(text: "${order.itemCount} ${"items".tr}"),
                      SizedBox(width: 8),
                      OrderTag(text: order.paymentMethod.name.tr),
                      Spacer(),
                      order.orderStatus.index > OrderStatus.executing.index
                          ? SizedBox()
                          : OrderTimer(
                              createdAt: order.createdAt,
                              orderStatus: order.orderStatus,
                              confirmed: order.executing,
                              processingTime: order.preparingTime,
                              handover: order.readyAt,
                            ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
