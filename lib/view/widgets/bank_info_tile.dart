import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/models/bank_info_model.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/providers/bank_info_provider.dart';

class BankInfoTile extends StatelessWidget {
  final BankInfoModel bankInfo;
  const BankInfoTile(this.bankInfo, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            // Optional: Add tap feedback or navigation
          },
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                // Bank icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: primaryColor.withValues(alpha: 0.1),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(12),
                    child: Image.asset(
                      "assets/icons/wallet_outline.png",
                      color: primaryColor,
                    ),
                  ),
                ),
                SizedBox(width: 16),
                // Bank info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "${bankInfo.bankIdName} - ${bankInfo.bankBranchIdName}",
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        bankInfo.personName,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[900],
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        bankInfo.accountNumber,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 12),
                // Action buttons
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Edit button
                    Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: primaryColor.withValues(alpha: 0.1),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(8),
                          onTap: () {
                            // TODO: Implement edit functionality when AddBankAccountScreen supports editing mode
                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                              content: Text(
                                  "Edit functionality will be implemented when AddBankAccountScreen supports editing mode"),
                              backgroundColor: Colors.orange,
                            ));
                          },
                          child: Icon(
                            Icons.edit_outlined,
                            size: 18,
                            color: primaryColor,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 8),
                    // Delete button
                    Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: errorColor.withValues(alpha: 0.1),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(8),
                          onTap: () async {
                            // Show confirmation dialog
                            bool? shouldDelete = await showDialog<bool>(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: Text("delete_bank_account".tr),
                                content:
                                    Text("delete_bank_account_confirmation".tr),
                                actions: [
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.pop(context, false),
                                    child: Text("cancel".tr),
                                  ),
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.pop(context, true),
                                    style: TextButton.styleFrom(
                                        foregroundColor: errorColor),
                                    child: Text("delete".tr),
                                  ),
                                ],
                              ),
                            );

                            if (shouldDelete == true && context.mounted) {
                              String? errorText =
                                  await Provider.of<BankInfoProvider>(context,
                                          listen: false)
                                      .deleteBankAccount(bankInfo.id);

                              if (errorText != null && context.mounted) {
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(SnackBar(
                                  content: Text(errorText),
                                  backgroundColor: errorColor,
                                ));
                              } else if (context.mounted) {
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(SnackBar(
                                  content: Text(
                                      "bank_account_deleted_successfully".tr),
                                  backgroundColor: successColor,
                                ));
                              }
                            }
                          },
                          child: Icon(
                            Icons.delete_outline,
                            size: 18,
                            color: errorColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
