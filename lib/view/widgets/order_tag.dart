import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class OrderTag extends StatelessWidget {
  final String text;
  const OrderTag({
    super.key,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        vertical: 2,
        // horizontal: 4,
      ),
      padding: EdgeInsets.all(7),
      decoration: BoxDecoration(
        color: successColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: Colors.white,
          fontSize: 10,
        ),
      ),
    );
  }
}
