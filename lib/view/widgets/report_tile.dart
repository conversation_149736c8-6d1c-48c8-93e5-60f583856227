import 'package:flutter/material.dart';
import 'package:hero_kit/data/base_urls.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_store_branch_app/data/urls.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_title.dart';

class ReportTile extends StatelessWidget {
  final String title;
  final String subtitle;
  final String image;
  const ReportTile({
    required this.title,
    required this.subtitle,
    required this.image,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: () => Navigator.of(context)
          .push(MaterialPageRoute(builder: (_) => ReportDetailsScreen())),
      dense: true,
      leading: Image.network(
        "${BaseUrls.domainUrl}${Urls.productImagePath}$image",
        errorBuilder: (context, error, stackTrace) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.asset("assets/images/food_default_image.png"),
          );
        },
      ),
      title: Text(
        title,
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 11,
          color: primaryColor,
        ),
      ),
    );
  }
}

class ReportDetailsScreen extends StatelessWidget {
  const ReportDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            AppBarWithTitle(
              "report_details".tr,
              backButton: true,
            ),
            Expanded(
              child: SingleChildScrollView(),
            ),
          ],
        ),
      ),
    );
  }
}
