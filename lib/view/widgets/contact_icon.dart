import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class ContactIcon extends StatelessWidget {
  final String? customerID;
  final String? customerNumber;
  final String icon;
  final void Function() onTap;
  const ContactIcon({
    this.customerID,
    this.customerNumber,
    required this.icon,
    required this.onTap,
    super.key,
  });

  factory ContactIcon.message(customerID) {
    return ContactIcon(
      customerID: customerID,
      icon: "message",
      onTap: () {},
    );
  }
  factory ContactIcon.call(customerNumber) {
    return ContactIcon(
      customerNumber: customerNumber,
      icon: "call",
      onTap: () {},
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(4),
      child: Material(
        borderRadius: BorderRadius.circular(8),
        clipBehavior: Clip.hardEdge,
        color: Colors.transparent,
        child: InkWell(
          splashColor: primaryColor.withAlpha(50),
          highlightColor: primaryColor.withAlpha(50),
          onTap: onTap,
          child: Ink(
            width: 32,
            height: 32,
            child: Image.asset("assets/icons/$icon.png"),
          ),
        ),
      ),
    );
  }
}
