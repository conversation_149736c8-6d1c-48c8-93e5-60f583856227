import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class PaginationWidget extends StatelessWidget {
  final int page;
  final void Function()? onBack;
  final void Function()? onNext;
  const PaginationWidget({
    required this.page,
    this.onBack,
    this.onNext,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.maxFinite,
      height: 48,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          SizedBox(),
          IconButton(
            onPressed: onBack,
            icon: Icon(Icons.arrow_back_ios_outlined),
            color: primaryColor,
            iconSize: 16,
          ),
          Text(
            "$page",
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: primaryColor,
            ),
          ),
          IconButton(
            onPressed: onNext,
            icon: Icon(Icons.arrow_forward_ios_outlined),
            color: primaryColor,
            iconSize: 16,
          ),
          SizedBox(),
        ],
      ),
    );
  }
}
