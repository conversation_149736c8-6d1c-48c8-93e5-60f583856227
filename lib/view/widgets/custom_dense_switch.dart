import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class CustomDenseSwitch extends StatelessWidget {
  final bool isActive;
  final void Function()? onTap;
  const CustomDenseSwitch({
    required this.isActive,
    this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.all(8),
        width: 30,
        height: 15,
        decoration: BoxDecoration(
          color: isActive ? primaryColor.withAlpha(38) : Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: AnimatedAlign(
          duration: Duration(milliseconds: 100),
          alignment: isActive ? AlignmentDirectional.centerStart : AlignmentDirectional.centerEnd,
          child: Container(
            // margin: EdgeInsets.symmetric(horizontal: 2),
            width: 15,
            height: 15,
            decoration: BoxDecoration(
              color: isActive ? primaryColor : Colors.grey[500],
              borderRadius: BorderRadius.circular(14),
            ),
          ),
        ),
      ),
    );
  }
}
