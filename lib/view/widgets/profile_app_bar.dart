import 'package:flutter/material.dart';

import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_store_branch_app/providers/auth_provider.dart';

class ProfileAppBar extends StatelessWidget {
  final String email;
  final String name;
  final String image;

  const ProfileAppBar({
    required this.name,
    required this.email,
    required this.image,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 190,
      child: Stack(
        children: [
          Container(
            height: 135,
            decoration: BoxDecoration(
              color: primaryColor,
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(12),
              ),
            ),
          ),
          Column(
            children: [
              Spacer(),
              Container(
                clipBehavior: Clip.hardEdge,
                width: 90,
                height: 90,
                decoration: BoxDecoration(
                  color: whiteColor,
                  shape: BoxShape.circle,
                ),
                child: Image.network(
                  AuthProvider().storeBranch.store.storeLogo,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) =>
                      Image.asset("assets/images/restaurant_default_image.png"),
                ),
              ),
              SizedBox(
                width: double.maxFinite,
                child: Text(
                  AuthProvider().storeBranch.name,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    // fontSize: 16,
                    color: blackColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
