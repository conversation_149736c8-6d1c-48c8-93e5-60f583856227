import 'package:flutter/material.dart';
import 'package:hero_kit/data/base_urls.dart';
import 'package:hero_kit/enums/delivery_type.dart';
import 'package:hero_kit/models/delivery_order_address.dart';
import 'package:hero_store_branch_app/data/urls.dart';
import 'package:hero_kit/enums/logger_enums.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_kit/services/logger.dart';
import 'package:hero_store_branch_app/view/widgets/contact_icon.dart';
import 'package:hero_store_branch_app/view/widgets/custom_divider.dart';

class CustomerDetailsWidget extends StatelessWidget {
  final DeliveryOrderAddress? deliveryOrderAddress;
  final Map customerData;
  const CustomerDetailsWidget(this.deliveryOrderAddress, this.customerData, {super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 120,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  clipBehavior: Clip.hardEdge,
                  width: 45,
                  height: 45,
                  child: Image.network(
                    "${BaseUrls.domainUrl}${Urls.restaurantavatarPath}",
                    errorBuilder: (context, error, stackTrace) {
                      Log("Image.network",
                          error: error, level: LogLevel.important, name: "CustomerDetailsWidget");
                      return Image.asset("assets/images/customer_default_image.png");
                    },
                  ),
                ),
                SizedBox(
                  width: 12,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Text(
                        // customerDetails.name,
                        deliveryOrderAddress?.name ?? customerData["name"],
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                      ),
                      Text(
                        // customerDetails.number,
                        deliveryOrderAddress?.phone ?? customerData["phone"],
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey,
                        ),
                        textDirection: TextDirection.ltr,
                      ),
                    ],
                  ),
                ),
                ContactIcon.message(deliveryOrderAddress?.phone ?? customerData["phone"]),
                ContactIcon.call(deliveryOrderAddress?.phone ?? customerData["phone"]),
              ],
            ),
          ),
          Text(
            "address".tr,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 13,
            ),
          ),
          Text(
            deliveryOrderAddress?.addressText ?? DeliveryType.inPlace.name.tr,
            style: TextStyle(
              fontSize: 11,
            ),
          ),
          CustomDivider(),
        ],
      ),
    );
  }
}
