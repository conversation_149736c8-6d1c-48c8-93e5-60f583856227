import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class SettingsListTile extends StatelessWidget {
  final void Function()? onTap;
  final String? leading;
  final Widget? trailing;
  final String title;
  const SettingsListTile(
    this.title, {
    this.onTap,
    this.leading,
    this.trailing,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Ink(
      height: 52,
      padding: EdgeInsets.symmetric(
        horizontal: 16,
      ),
      child: Column(
        children: [
          Expanded(
            child: InkWell(
              splashColor: transparentColor,
              overlayColor: WidgetStateProperty.all(transparentColor),
              onTap: onTap,
              child: Row(
                children: [
                  Image.asset(
                    leading!,
                    scale: 2,
                    color: primaryColor.dark,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                    ),
                  ),
                  SizedBox(width: 8),
                  trailing ??
                      Icon(
                        Icons.arrow_forward_ios_outlined,
                        size: 16,
                        color: primaryColor.dark,
                      )
                ],
              ),
            ),
          ),
          Container(
            width: double.maxFinite,
            height: 1,
            color: backgroundText,
          )
        ],
      ),
    );
  }
}
