import 'package:flutter/material.dart';
import 'package:hero_kit/data/base_urls.dart';
import 'package:hero_kit/models/products/delivery_product.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_store_branch_app/data/urls.dart';
import 'package:hero_kit/enums/logger_enums.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_kit/services/logger.dart';
import 'package:hero_store_branch_app/view/widgets/custom_divider.dart';

class MealTile extends StatelessWidget {
  final DeliveryProduct deliveryProduct;
  const MealTile(this.deliveryProduct, {super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 80,
      child: Column(
        children: [
          Spacer(),
          Row(
            children: [
              Text(
                "X",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: primaryColor,
                ),
              ),
              Text(
                "${deliveryProduct.product.quantity}",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: primaryColor,
                ),
              ),
              SizedBox(width: 12),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                clipBehavior: Clip.hardEdge,
                width: 55,
                height: 55,
                child: Image.network(
                  "${BaseUrls.domainUrl}${Urls.productImagePath}/${deliveryProduct.product.image}",
                  errorBuilder: (context, error, stackTrace) {
                    Log("Image.network", error: error, level: LogLevel.important, name: "MealTile");
                    return Image.asset("assets/images/food_default_image.png");
                  },
                ),
              ),
              SizedBox(width: 8),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    deliveryProduct.product.name,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 13,
                    ),
                  ),
                  Text(
                    "${(deliveryProduct.product.price * 100).round() / 100} ${currency.tr}",
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                      color: primaryColor,
                    ),
                  ),
                ],
              )
            ],
          ),
          Spacer(),
          CustomDivider()
        ],
      ),
    );
  }
}
