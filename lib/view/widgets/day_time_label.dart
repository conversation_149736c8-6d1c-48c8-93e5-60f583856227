import 'package:flutter/material.dart';

class DayTimeLabel extends StatelessWidget {
  final String text;
  final void Function()? onTap;
  const DayTimeLabel(
    this.text, {
    this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.all(8),
        padding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 240, 240, 240),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            SizedBox(width: 4),
            Text(
              text,
              textDirection: TextDirection.ltr,
              style: TextStyle(fontSize: 12),
            ),
            SizedBox(width: 6),
            Image.asset(
              "assets/icons/drop_arrow_down.png",
              width: 18,
              height: 18,
              color: Colors.grey,
            ),
            SizedBox(width: 6),
          ],
        ),
      ),
    );
  }
}
