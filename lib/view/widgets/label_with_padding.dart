import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class LabelWithPadding extends StatelessWidget {
  final String text;
  const LabelWithPadding(
    this.text, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Text(text, style: labelStyle),
    );
  }
}
