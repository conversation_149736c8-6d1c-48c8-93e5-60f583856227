import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class OneSideCard extends StatelessWidget {
  final String label;
  final String value;
  final bool top;
  final String image;
  const OneSideCard({
    required this.label,
    required this.value,
    required this.top,
    required this.image,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Flexible(
      flex: 5,
      child: Container(
        decoration: BoxDecoration(
          color: primaryColor.withAlpha(21),
          borderRadius: top
              ? BorderRadius.vertical(top: Radius.circular(12))
              : BorderRadius.vertical(bottom: Radius.circular(12)),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 12,
        ),
        height: 100,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Spacer(),
            Row(),
            Image.asset(
              scale: 2,
              image,
              color: primaryColor,
            ),
            Spacer(),
            Text(
              label,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.bold,
              ),
            ),
            Row(
              children: [
                Text(
                  _formatValue(value),
                  style: TextStyle(fontSize: 13, color: primaryColor),
                ),
              ],
            ),
            Spacer(),
          ],
        ),
      ),
    );
  }

  String _formatValue(String value) {
    // Check if value already contains currency symbols
    if (value.contains('₪') ||
        value.contains('\$') ||
        value.contains('€') ||
        value.contains('£')) {
      return value;
    }
    // If it's just a number, add the default currency
    return "$value $currency";
  }
}
