import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class DigitField extends StatelessWidget {
  final String field;
  final int index;
  final void Function()? onTap;
  const DigitField(this.field, this.index, {this.onTap, super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 44,
        height: 43,
        // margin: EdgeInsets.all(3),
        decoration: BoxDecoration(
          color: fillLabelsColor,
          border: Border.all(color: bordersColor),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Center(child: Text(field.length > index ? field[index] : "")),
      ),
    );
  }
}
