import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class AppBarIconButton extends StatelessWidget {
  final String icon;

  final void Function()? onTap;
  const AppBarIconButton({
    super.key,
    required this.icon,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: BorderRadius.circular(32),
      color: Color.fromARGB(255, 238, 245, 251),
      clipBehavior: Clip.hardEdge,
      child: InkWell(
        onTap: onTap,
        child: Ink(
          width: AppBar().preferredSize.height * .8,
          height: AppBar().preferredSize.height / 2,
          padding: EdgeInsets.all(4),
          child: Image.asset(
            icon,
            fit: BoxFit.contain,
            color: primaryColor,
          ),
        ),
      ),
    );
  }
}
