import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class DenseListTile extends StatelessWidget {
  final String? icon;
  final void Function()? onTap;
  final String text;
  const DenseListTile(
    this.text, {
    this.icon,
    this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onTap,
      dense: true,
      visualDensity: VisualDensity.compact,
      leading: icon == null
          ? null
          : Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: unimplementedColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Center(
                child: Image.asset(
                  icon!,
                  width: 28,
                  height: 28,
                ),
              ),
            ),
      title: Text(
        text,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }
}
