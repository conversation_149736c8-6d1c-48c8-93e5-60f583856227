import 'package:flutter/material.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_store_branch_app/providers/orders_provider.dart';

class OrderlistFilterCard extends StatelessWidget {
  final int count;
  final String text;
  const OrderlistFilterCard({
    super.key,
    required this.count,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Selector<OrdersProvider, bool>(
      selector: (_, orderProvider) => orderProvider.selectedStatusFilter == text,
      builder: (_, isSelected, ___) => Material(
        color: isSelected ? primaryColor : primaryColor.withAlpha(38),
        borderRadius: BorderRadius.circular(4),
        clipBehavior: Clip.hardEdge,
        child: InkWell(
          onTap: () => Provider.of<OrdersProvider>(context, listen: false).selectStatusFilter(text),
          child: Ink(
            padding: EdgeInsets.all(6),
            width: MediaQuery.of(context).size.width / 5,
            height: 55,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Container(
                  height: 24,
                  width: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected ? Colors.white : primaryColor.normalHover,
                  ),
                  child: Center(
                    child: Text(
                      "$count",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isSelected ? primaryColor : Colors.white,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ),
                Text(
                  text.tr,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
