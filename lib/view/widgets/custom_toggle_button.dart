import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class CustomToggleButton extends StatelessWidget {
  final bool active;
  final String text;
  final double? fontSize;
  final void Function()? onTap;
  const CustomToggleButton(
    this.text, {
    required this.active,
    this.onTap,
    this.fontSize,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 6),
        child: Material(
          clipBehavior: Clip.hardEdge,
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          child: InkWell(
            onTap: onTap,
            child: Ink(
              color: active ? primaryColor : primaryColor.light,
              child: Center(
                child: Text(
                  text,
                  style: TextStyle(
                    color: active ? Colors.white : Colors.black,
                    fontSize: fontSize,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
