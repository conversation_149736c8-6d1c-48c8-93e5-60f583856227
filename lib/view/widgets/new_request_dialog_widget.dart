import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/otlub_vendor_app.dart';
import 'package:hero_store_branch_app/providers/orders_provider.dart';

class NewRequestDialogWidget extends StatelessWidget {
  const NewRequestDialogWidget({super.key});

  @override
  Widget build(BuildContext context) {
    AudioPlayer audio = AudioPlayer();
    audio.play(AssetSource('sounds/reminder.wav'));
    Timer timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      audio.play(AssetSource('sounds/reminder.wav'));
    });
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          Image.asset("assets/images/notification_in.png",
              height: 60, color: Theme.of(context).primaryColor),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'new_order_placed'.tr,
              textAlign: TextAlign.center,
            ),
          ),
          ElevatedButton(
            style: elevatedButtonStyle,
            onPressed: () {
              timer.cancel();
              Provider.of<OrdersProvider>(navigatorKey.currentContext!, listen: false).alertShown =
                  false;
              audio.stop();
              Navigator.of(context).pop();
            },
            child: Text('ok'.tr),
          ),
        ]),
      ),
    );
  }
}

// class NewRequestDialogWidget extends StatefulWidget {
//   const NewRequestDialogWidget({super.key});

//   @override
//   State<NewRequestDialogWidget> createState() => _NewRequestDialogWidgetState();
// }

// class _NewRequestDialogWidgetState extends State<NewRequestDialogWidget> {
//   Timer? _timer;

//   @override
//   void initState() {
//     super.initState();
//     _startAlarm();
//     Provider.of<OrdersProvider>(navigatorKey.currentContext!, listen: false).alertShown = true;
//   }

//   @override
//   void dispose() {
//     super.dispose();
//     _timer?.cancel();
//   }

//   void _startAlarm() async {
//     AudioPlayer audio = AudioPlayer();
//     audio.play(AssetSource('sounds/notification.wav'));
//     _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
//       audio.play(AssetSource('sounds/notification.wav'));
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
//       child: Padding(
//         padding: const EdgeInsets.all(16),
//         child: Column(mainAxisSize: MainAxisSize.min, children: [
//           Image.asset("assets/images/logo.png", height: 60, color: Theme.of(context).primaryColor),
//           Padding(
//             padding: const EdgeInsets.all(16),
//             child: Text(
//               'new_order_placed'.tr,
//               textAlign: TextAlign.center,
//             ),
//           ),
//           ElevatedButton(
//             style: elevatedButtonStyle,
//             onPressed: () {
//               _timer?.cancel();
//               Provider.of<OrdersProvider>(navigatorKey.currentContext!, listen: false).alertShown =
//                   false;
//               Navigator.of(context).pop();
//             },
//             child: Text('ok'.tr),
//           ),
//         ]),
//       ),
//     );
//   }
// }
