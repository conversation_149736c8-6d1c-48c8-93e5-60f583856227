import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class CustomBottomSheet extends StatelessWidget {
  final List<Widget> children;
  const CustomBottomSheet({
    required this.children,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      // height: MediaQuery.of(context).size.height / 3,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                margin: EdgeInsets.symmetric(vertical: 16),
                height: 4,
                width: MediaQuery.of(context).size.width / 4,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 24),
          ...children,
          SizedBox(height: 24),
        ],
      ),
    );
  }
}
