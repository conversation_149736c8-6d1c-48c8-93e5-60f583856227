import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_store_branch_app/models/notification_model.dart';

class NotificationTile extends StatelessWidget {
  final void Function()? onTap;
  final NotificationModel n;
  const NotificationTile(
    this.n, {
    this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      child: InkWell(
        onTap: onTap,
        child: Ink(
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 12),
          decoration: BoxDecoration(
            border: Border(
                bottom: BorderSide(
              width: .5,
              color: Colors.grey[300]!,
            )),
          ),
          child: Row(
            children: [
              Ink(
                width: 36,
                height: 36,
                padding: EdgeInsets.all(4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(18),
                  color: primaryColor.withAlpha(38),
                ),
                child: n.image == null
                    ? Image.asset("assets/icons/notification.png")
                    : Image.network(n.image!),
              ),
              SizedBox(width: 16),
              Text(
                n.title,
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
