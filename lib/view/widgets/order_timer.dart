import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/enums/order_status.dart';
import 'package:hero_kit/extensions/duration_extension.dart';
import 'package:hero_store_branch_app/providers/orders_provider.dart';

class OrderTimer extends StatelessWidget {
  final OrderStatus orderStatus;
  final DateTime createdAt;
  final DateTime? confirmed;
  final Duration? processingTime;
  final DateTime? handover;

  const OrderTimer({
    required this.orderStatus,
    required this.createdAt,
    this.confirmed,
    this.processingTime,
    this.handover,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Selector<OrdersProvider, bool>(
      selector: (_, orderPrivider) => orderPrivider.ticker,
      builder: (_, __, ___) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 6, horizontal: 12),
          decoration: BoxDecoration(
            color: getFormattedColor(),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            children: [
              Image.asset(
                "assets/icons/time_square_filled.png",
                width: 12,
                height: 12,
                color: whiteColor,
              ),
              SizedBox(width: 4),
              Text(
                getFormattedTime(),
                style: TextStyle(color: Colors.white, fontSize: 10),
              )
            ],
          ),
        );
      },
    );
  }

  Color getFormattedColor() {
    if (orderStatus == OrderStatus.executing) {
      return confirmed!.add(processingTime!).difference(DateTime.now()).inSeconds > 0
          ? primaryColor
          : errorColor;
    }
    return primaryColor;
  }

  String getFormattedTime() {
    if (orderStatus == OrderStatus.pending) {
      return DateTime.now().difference(createdAt).abs().format();
    }
    if (orderStatus == OrderStatus.executing) {
      return confirmed!.add(processingTime!).difference(DateTime.now()).abs().format();
    }
    if (orderStatus == OrderStatus.onTheWay) {
      return DateTime.now().difference(handover!).abs().format();
    }
    return "~";
  }
}
