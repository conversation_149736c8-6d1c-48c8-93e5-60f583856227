import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class CustomSwitch extends StatelessWidget {
  final bool active;
  const CustomSwitch({
    required this.active,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 36,
      height: 18,
      decoration: BoxDecoration(
        color: active ? primaryColor : Colors.grey,
        borderRadius: BorderRadius.circular(18),
      ),
      child: AnimatedAlign(
        duration: Duration(milliseconds: 100),
        alignment: active ? AlignmentDirectional.centerEnd : AlignmentDirectional.centerStart,
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 2),
          width: 15,
          height: 15,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(14),
          ),
        ),
      ),
    );
  }
}
