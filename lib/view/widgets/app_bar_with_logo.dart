import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/providers/auth_provider.dart';
import 'package:hero_store_branch_app/providers/store_branch_work_time_provider.dart';
import 'package:hero_store_branch_app/view/screens/notification_screen/notification_screen.dart';
import 'package:hero_store_branch_app/view/screens/restaurant_option_screen/restaurant_option_screen.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_icon_button.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_title.dart';
import 'package:hero_store_branch_app/view/widgets/custom_bottom_sheet.dart';
import 'package:hero_store_branch_app/view/widgets/dense_list_tile.dart';

class AppBarWithLogo extends StatelessWidget {
  const AppBarWithLogo({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12),
      height: 95,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[200]!,
          ),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppBarIconButton(
                icon: "assets/icons/menu.png",
                onTap: () => showModalBottomSheet(
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  barrierColor: Colors.white30,
                  context: context,
                  builder: (cContext) => CustomBottomSheet(
                    children: [
                      DenseListTile(
                        "update_order_list".tr,
                        icon: "assets/icons/refresh.png",
                        onTap: () {
                          // Provider.of<OrdersProvider>(context, listen: false).refreshList();
                          Navigator.of(cContext).pop();
                        },
                      ),
                      DenseListTile(
                        "search_for_orders".tr,
                        icon: "assets/icons/search.png",
                        onTap: () {
                          Navigator.of(cContext).pop();
                          Navigator.of(context)
                              .push(MaterialPageRoute(builder: (_) => SearchResultScreen()));
                        },
                      ),
                      DenseListTile(
                        "restaurant_options".tr,
                        icon: "assets/icons/data.png",
                        onTap: () {
                          Navigator.of(context).pop();
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (_) => ChangeNotifierProvider(
                                create: (_) => StoreBranchWorkTimeProvider.from(
                                  authProvider: AuthProvider(),
                                ),
                                child: RestaurantOptionScreen(),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
              Image.asset(
                "assets/images/logo.png",
                color: primaryColor,
                height: AppBar().preferredSize.height / 2,
                fit: BoxFit.fitHeight,
              ),
              AppBarIconButton(
                icon: "assets/icons/notification_filled.png",
                onTap: () => Navigator.of(context)
                    .push(MaterialPageRoute(builder: (_) => NotificationScreen())),
              ),
            ],
          ),
          SizedBox(height: 14),
        ],
      ),
    );
  }
}

class SearchResultScreen extends StatelessWidget {
  const SearchResultScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          AppBarWithTitle(
            "صفحة البحث",
            backButton: true,
          ),
          Expanded(
            child: Placeholder(),
          ),
        ],
      ),
    );
  }
}
