import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class CustomDatePicker extends StatelessWidget {
  final DateTime? dateTime;
  final DateTime minDate;
  final DateTime maxDate;
  final String label;
  final bool expanded;
  final void Function(DateTime newDateTime) onPickedNewDate;
  const CustomDatePicker({
    required this.label,
    this.dateTime,
    required this.minDate,
    required this.maxDate,
    required this.onPickedNewDate,
    this.expanded = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        var child = Padding(
          padding: EdgeInsets.symmetric(horizontal: 8),
          child: Ink(
            height: 40,
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              border: Border.all(
                color: greyColor,
                width: .5,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: InkWell(
              hoverColor: transparentColor,
              overlayColor: WidgetStateProperty.all(transparentColor),
              onTap: () async {
                DateTime? newDateTime = await showDatePicker(
                  initialEntryMode: DatePickerEntryMode.calendarOnly,
                  context: context,
                  firstDate: minDate,
                  lastDate: maxDate,
                );
                if (newDateTime != null) {
                  onPickedNewDate(newDateTime);
                }
              },
              child: Row(
                children: [
                  Text(
                    "$label:",
                    style: TextStyle(
                      color: greyColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      dateTime == null
                          ? ""
                          : "${dateTime?.day}/${dateTime?.month}/${dateTime?.year}",
                      style: TextStyle(
                        color: blackColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(
                    height: 24,
                    width: 24,
                    child: Image.asset(
                      "assets/icons/calendar_outline.png",
                      color: primaryColor,
                    ),
                  )
                ],
              ),
            ),
          ),
        );
        return expanded
            ? Expanded(
                child: child,
              )
            : child;
      },
    );
  }
}

class CustomDurationPicker extends StatelessWidget {
  final DateTime? dateTime;
  final String label;

  final void Function(DateTime newDateTime) onPickedNewDate;
  const CustomDurationPicker({
    required this.label,
    this.dateTime,
    required this.onPickedNewDate,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    DateTime? weekAdded = dateTime?.add(Duration(days: 6));
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8),
      child: Ink(
        height: 40,
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          border: Border.all(
            color: greyColor,
            width: .5,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: InkWell(
          hoverColor: transparentColor,
          overlayColor: WidgetStateProperty.all(transparentColor),
          onTap: () async {
            DateTime? newDateTime = await showDatePicker(
              initialEntryMode: DatePickerEntryMode.calendarOnly,
              context: context,
              firstDate: DateTime.fromMillisecondsSinceEpoch(0),
              lastDate: DateTime.now(),
            );
            if (newDateTime != null) {
              onPickedNewDate(newDateTime);
            }
          },
          child: Row(
            children: [
              Text(
                "$label:",
                style: TextStyle(
                  color: greyColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  dateTime == null
                      ? ""
                      : "${dateTime?.day}/${dateTime?.month}/${dateTime?.year}  -  ${weekAdded?.day}/${weekAdded?.month}/${weekAdded?.year}",
                  style: TextStyle(
                    color: blackColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(
                height: 24,
                width: 24,
                child: Image.asset(
                  "assets/icons/calendar_outline.png",
                  color: primaryColor,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class CustomMonthPicker extends StatelessWidget {
  final DateTime? dateTime;
  final String label;

  final void Function(DateTime newDateTime) onPickedNewDate;
  const CustomMonthPicker({
    required this.label,
    this.dateTime,
    required this.onPickedNewDate,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8),
      child: Ink(
        height: 40,
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          border: Border.all(
            color: greyColor,
            width: .5,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: InkWell(
          hoverColor: transparentColor,
          overlayColor: WidgetStateProperty.all(transparentColor),
          onTap: () async {
            DateTime? newDateTime = await showDatePicker(
              initialEntryMode: DatePickerEntryMode.calendarOnly,
              context: context,
              firstDate: DateTime.fromMillisecondsSinceEpoch(0),
              lastDate: DateTime.now(),
            );
            if (newDateTime != null) {
              onPickedNewDate(newDateTime);
            }
          },
          child: Row(
            children: [
              Text(
                "$label:",
                style: TextStyle(
                  color: greyColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  dateTime == null ? "" : "${dateTime?.month}/${dateTime?.year}",
                  style: TextStyle(
                    color: blackColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(
                height: 24,
                width: 24,
                child: Image.asset(
                  "assets/icons/calendar_outline.png",
                  color: primaryColor,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class CustomYearPicker extends StatelessWidget {
  final DateTime? dateTime;
  final String label;

  final void Function(DateTime newDateTime) onPickedNewDate;
  const CustomYearPicker({
    required this.label,
    this.dateTime,
    required this.onPickedNewDate,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final TextStyle labelTextStyle = TextStyle(fontSize: 13, color: Colors.grey[700]);
    final OutlineInputBorder outlinedInputBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(
        color: Colors.grey[300]!,
        width: .5,
      ),
    );
    late final InputDecorationTheme inputDecorationTheme = InputDecorationTheme(
      isDense: true,
      // labelStyle: TextStyle(fontSize: 8),

      contentPadding: EdgeInsets.symmetric(vertical: 0, horizontal: 8),
      constraints: BoxConstraints.tight(
        Size(
          double.maxFinite,
          42,
        ),
      ),
      border: outlinedInputBorder,
      enabledBorder: outlinedInputBorder,
      focusedBorder: outlinedInputBorder,
      focusedErrorBorder: outlinedInputBorder,
      hintStyle: labelStyle.copyWith(color: Colors.grey[500]),
    );
    return DropdownMenu(
      menuStyle: MenuStyle(
        backgroundColor: WidgetStateProperty.all(whiteColor),
        maximumSize: WidgetStateProperty.all(
          Size(
            double.maxFinite,
            MediaQuery.of(context).size.height / 2,
          ),
        ),
        padding: WidgetStateProperty.all(EdgeInsets.zero),
      ),
      textStyle: labelTextStyle,
      width: MediaQuery.of(context).size.width - 32,
      initialSelection: DateTime.now().year,
      trailingIcon: Icon(Icons.keyboard_arrow_down_rounded),
      selectedTrailingIcon: Icon(Icons.keyboard_arrow_down_rounded),
      inputDecorationTheme: inputDecorationTheme,
      dropdownMenuEntries: List.generate(
        DateTime.now().year - 2024,
        (i) => DropdownMenuEntry(
          style: ButtonStyle(
            textStyle: WidgetStateProperty.all(labelTextStyle),
          ),
          value: DateTime.now().year - i,
          label: "${DateTime.now().year - i}",
        ),
      ),
      onSelected: (value) {
        if (value == null) return;
        onPickedNewDate(DateTime(value));
      },
    );
  }
}
