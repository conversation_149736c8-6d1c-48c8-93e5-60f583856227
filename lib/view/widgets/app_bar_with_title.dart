import 'package:flutter/material.dart';

class AppBarWithTitle extends StatelessWidget {
  final String title;
  final bool backButton;
  final Widget? trailer;
  const AppBarWithTitle(
    this.title, {
    this.backButton = false,
    this.trailer,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
          right: 12, left: 12, top: MediaQuery.of(context).padding.top),
      child: <PERSON>um<PERSON>(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              backButton
                  ? IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(Icons.arrow_back_ios_new),
                    )
                  : SizedBox(width: 32),
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              trailer ?? SizedBox(width: 32),
            ],
          ),
          SizedBox(height: 20),
        ],
      ),
    );
  }
}
