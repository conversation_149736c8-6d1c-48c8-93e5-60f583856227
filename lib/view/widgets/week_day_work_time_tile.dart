import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/providers/store_branch_work_time_provider.dart';
import 'package:hero_store_branch_app/view/widgets/custom_dense_switch.dart';
import 'package:hero_store_branch_app/view/widgets/day_time_label.dart';

class WeekDayWorkTimeTile extends StatefulWidget {
  final int index;
  const WeekDayWorkTimeTile(
    this.index, {
    super.key,
  });

  @override
  State<WeekDayWorkTimeTile> createState() => _WeekDayWorkTimeTileState();
}

class _WeekDayWorkTimeTileState extends State<WeekDayWorkTimeTile> {
  bool expanded = false;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 250, 250, 250),
        borderRadius: BorderRadius.circular(8),
      ),
      margin: EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      // padding: EdgeInsets.symmetric(vertical: 4),
      child: Column(
        children: [
          Material(
            clipBehavior: Clip.hardEdge,
            borderRadius: BorderRadius.circular(8),
            color: Colors.transparent,
            child: InkWell(
              onTap: () => setState(() {
                expanded = !expanded;
              }),
              child: Ink(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Row(
                  children: [
                    Selector<StoreBranchWorkTimeProvider, bool>(
                      selector: (_, restaurantWorkTimeProvider) =>
                          restaurantWorkTimeProvider.workDays[widget.index].working,
                      builder: (_, isWorking, __) => CustomDenseSwitch(
                        isActive: isWorking,
                        onTap: () =>
                            Provider.of<StoreBranchWorkTimeProvider>(context, listen: false)
                                .setWorking(widget.index, !isWorking),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        DateFormat.EEEE(Localizations.localeOf(context).languageCode).format(
                          DateTime(
                            2023,
                            7,
                            1 +
                                Provider.of<StoreBranchWorkTimeProvider>(
                                  context,
                                  listen: false,
                                ).workDays[widget.index].day,
                          ),
                        ),
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 13,
                        ),
                      ),
                    ),
                    Image.asset(
                      expanded
                          ? "assets/icons/drop_arrow_up.png"
                          : "assets/icons/drop_arrow_down.png",
                      width: 28,
                      height: 28,
                    ),
                  ],
                ),
              ),
            ),
          ),
          expanded
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Selector<StoreBranchWorkTimeProvider, TimeOfDay>(
                      selector: (_, restaurantWorkTimeProvider) =>
                          restaurantWorkTimeProvider.workDays[widget.index].openingTime,
                      builder: (_, openingTime, __) => DayTimeLabel(
                        openingTime.format(context),
                        onTap: () async {
                          TimeOfDay? newStartTime = await showTimePicker(
                            context: context,
                            initialTime: openingTime,
                            initialEntryMode: TimePickerEntryMode.dial,
                          );
                          if (context.mounted && newStartTime != null) {
                            Provider.of<StoreBranchWorkTimeProvider>(context, listen: false)
                                .setStartTime(newStartTime, widget.index);
                          }
                        },
                      ),
                    ),
                    Selector<StoreBranchWorkTimeProvider, TimeOfDay>(
                      selector: (_, restaurantWorkTimeProvider) =>
                          restaurantWorkTimeProvider.workDays[widget.index].closingTime,
                      builder: (_, closingTime, __) => DayTimeLabel(
                        closingTime.format(context),
                        onTap: () async {
                          TimeOfDay? newEndTime = await showTimePicker(
                            context: context,
                            initialTime: closingTime,
                            initialEntryMode: TimePickerEntryMode.dial,
                          );
                          if (context.mounted && newEndTime != null) {
                            Provider.of<StoreBranchWorkTimeProvider>(context, listen: false)
                                .setEndTime(newEndTime, widget.index);
                          }
                        },
                      ),
                    ),
                  ],
                )
              : SizedBox(),
        ],
      ),
    );
  }
}
