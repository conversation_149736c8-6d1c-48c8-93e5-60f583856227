import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class BottomAppBarButton extends StatelessWidget {
  final String image;
  final String filled;
  final String text;
  final bool isSelected;
  final void Function()? onTap;

  const BottomAppBarButton({
    required this.image,
    required this.filled,
    required this.text,
    this.onTap,
    required this.isSelected,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: Material(
        child: InkWell(
          onTap: onTap,
          child: Ink(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(
                  isSelected ? filled : image,
                  width: 28,
                  height: 28,
                  color: isSelected ? primaryColor : Colors.grey,
                ),
                Text(
                  text,
                  style: TextStyle(
                    fontSize: 11,
                    color: isSelected ? primaryColor : Colors.grey,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
