import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class CostTile extends StatelessWidget {
  final String title;
  final String cost;
  final bool active;
  final bool disabled;
  const CostTile({
    required this.title,
    required this.cost,
    this.active = true,
    this.disabled = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 13,
            color: disabled ? Colors.grey : null,
          ),
        ),
        Text(
          "$cost $currency",
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 13,
            color: disabled
                ? Colors.grey
                : active
                    ? primaryColor
                    : null,
          ),
        ),
      ],
    );
  }
}
