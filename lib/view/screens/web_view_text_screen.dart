import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_title.dart';

class WebViewTextScreen extends StatelessWidget {
  final String title;
  final String? link;
  final String? text;

  const WebViewTextScreen(
    this.title, {
    this.link,
    this.text,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          AppBarWithTitle(
            title,
            backButton: true,
          ),
          Expanded(
            child: RichText(
              text: TextSpan(
                text: link ?? text,
                style: TextStyle(
                  color: Colors.red,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
