import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:provider/provider.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/providers/navigator_provider.dart';
import 'package:hero_store_branch_app/view/pages/call_delivery_page.dart';
import 'package:hero_store_branch_app/view/pages/finance_page.dart';
import 'package:hero_store_branch_app/view/pages/main_page.dart';
import 'package:hero_store_branch_app/view/pages/complete_order_page.dart';
import 'package:hero_store_branch_app/view/pages/settings_page.dart';
import 'package:hero_store_branch_app/view/widgets/bottom_app_bar_button.dart';

class MainScreen extends StatelessWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: whiteColor,
      body: SafeArea(
        child: ChangeNotifierProvider(
          create: (_) => NavigatorProvider(),
          builder: (context, child) {
            return Column(
              children: [
                Expanded(
                  child: Selector<NavigatorProvider, int>(
                    selector: (_, navigatorProvider) =>
                        navigatorProvider.currentPage,
                    builder: (context, currentPage, child) => [
                      MainPage(),
                      CompleteOrderPage(),
                      CallDeliveryPage(),
                      FinancePage(),
                      SettingsPage(),
                    ][Provider.of<NavigatorProvider>(context).currentPage],
                  ),
                ),
                SizedBox(
                  width: double.maxFinite,
                  height: 70,
                  child: Row(
                    children: [
                      BottomAppBarButton(
                        image: "assets/icons/home_outline.png",
                        filled: "assets/icons/home_filled.png",
                        text: "main_page".tr,
                        isSelected: Provider.of<NavigatorProvider>(context)
                                .currentPage ==
                            0,
                        onTap: () => Provider.of<NavigatorProvider>(context,
                                listen: false)
                            .setActivePage(0),
                      ),
                      BottomAppBarButton(
                        image: "assets/icons/order_outline.png",
                        filled: "assets/icons/order_filled.png",
                        text: "my_orders".tr,
                        isSelected: Provider.of<NavigatorProvider>(context)
                                .currentPage ==
                            1,
                        onTap: () => Provider.of<NavigatorProvider>(context,
                                listen: false)
                            .setActivePage(1),
                      ),
                      BottomAppBarButton(
                        image: "assets/icons/motor_outline.png",
                        filled: "assets/icons/motor_filled.png",
                        text: "call_delivery".tr,
                        isSelected: Provider.of<NavigatorProvider>(context)
                                .currentPage ==
                            2,
                        onTap: () => Provider.of<NavigatorProvider>(context,
                                listen: false)
                            .setActivePage(2),
                      ),
                      BottomAppBarButton(
                        image: "assets/icons/budget_outline.png",
                        filled: "assets/icons/budget_filled.png",
                        text: "financial".tr,
                        isSelected: Provider.of<NavigatorProvider>(context)
                                .currentPage ==
                            3,
                        onTap: () => Provider.of<NavigatorProvider>(context,
                                listen: false)
                            .setActivePage(3),
                      ),
                      BottomAppBarButton(
                        image: "assets/icons/settings_outline.png",
                        filled: "assets/icons/settings_filled.png",
                        text: "settings".tr,
                        isSelected: Provider.of<NavigatorProvider>(context)
                                .currentPage ==
                            4,
                        onTap: () => Provider.of<NavigatorProvider>(context,
                                listen: false)
                            .setActivePage(4),
                      ),
                    ],
                  ),
                )
              ],
            );
          },
        ),
      ),
    );
  }
}
