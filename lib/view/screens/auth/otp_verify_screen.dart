import 'dart:async';

import 'package:flutter/material.dart';
import 'package:hero_kit/extensions/duration_extension.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/providers/auth_provider.dart';
import 'package:hero_store_branch_app/view/widgets/digit_field.dart';
import 'package:provider/provider.dart';

class OtpVerifyScreen extends StatefulWidget {
  final String email;
  const OtpVerifyScreen({
    required this.email,
    super.key,
  });

  @override
  State<OtpVerifyScreen> createState() => _OtpVerifyScreenState();
}

class _OtpVerifyScreenState extends State<OtpVerifyScreen> {
  final FocusNode focusNode = FocusNode();
  String otp = "";
  Duration cooldown = Duration(seconds: 30);
  late Timer cooldownTimer;
  bool validOtp = false;
  bool _isBusy = false;

  @override
  void initState() {
    cooldownTimer = Timer.periodic(Duration(seconds: 1), tick);
    super.initState();
  }

  @override
  void dispose() {
    cooldownTimer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          SizedBox(height: kToolbarHeight),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  Text(
                    "verify_otp_title".tr,
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: primaryColor.darkHover,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    "verify_otp_instruction".tr,
                    style: TextStyle(fontSize: 16, color: blackColor),
                  ),
                  const SizedBox(height: 40),
                  Row(
                    textDirection: TextDirection.ltr,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: cooldown.inSeconds > 0 ? null : resendCode,
                          style: ElevatedButton.styleFrom(
                            elevation: 0,
                            disabledForegroundColor: blackColor,
                            disabledBackgroundColor: fillLabelsColor,
                            backgroundColor: primaryColor.dark,
                            foregroundColor: whiteColor,
                            minimumSize: Size(double.maxFinite, 43),
                          ),
                          child: Text("resend_code".tr, overflow: TextOverflow.ellipsis),
                        ),
                      ),
                      SizedBox(width: 6),
                      Row(
                        textDirection: TextDirection.ltr,
                        children: [
                          DigitField(otp, 0, onTap: () => focusField(context)),
                          SizedBox(width: 6),
                          DigitField(otp, 1, onTap: () => focusField(context)),
                          SizedBox(width: 6),
                          DigitField(otp, 2, onTap: () => focusField(context)),
                          SizedBox(width: 6),
                          DigitField(otp, 3, onTap: () => focusField(context)),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  SizedBox(
                    height: 16,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        cooldown.inSeconds > 0
                            ? Text(
                                "${"can_resend_verificatin_code_in".tr} ${cooldown.mmss}",
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                  color: errorColor,
                                ),
                              )
                            : SizedBox(),
                      ],
                    ),
                  ),
                  SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryColor,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
                      ),
                      onPressed: validOtp && !_isBusy ? _verifyOtp : null,
                      child: Text(
                        "confirm".tr,
                        style: TextStyle(fontSize: 18, color: Colors.white),
                      ),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "wrong_phone_number".tr,
                        style: TextStyle(
                          color: blackColor,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          "edit_phone_number".tr,
                          style: TextStyle(
                            color: primaryColor.normal,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Opacity(
                    opacity: 0,
                    child: SizedBox(
                      height: 0,
                      child: TextField(
                        textDirection: TextDirection.ltr,
                        keyboardType: TextInputType.number,
                        focusNode: focusNode,
                        onChanged: (value) => setState(() {
                          otp = value;
                          validOtp = otp.length == 4;
                        }),
                        maxLength: 4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _verifyOtp() async {
    if (_isBusy) return;
    setState(() {
      _isBusy = true;
    });

    String? verifyError =
        await Provider.of<AuthProvider>(context, listen: false).verifyOtp(widget.email, otp);

    if (!context.mounted) return;

    setState(() {
      _isBusy = false;
    });

    if (verifyError != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(verifyError),
          backgroundColor: errorColor,
        ),
      );
      return;
    }
    Navigator.of(context).popUntil((r) => r.isFirst);
  }

  Future<void> resendCode() async {
    // Map<String, dynamic> response = await AuthRepo().login(widget.phone);
    // if (!context.mounted) return;
    // if (response["success"] != true) {
    //   ScaffoldMessenger.of(context).showSnackBar(
    //     SnackBar(
    //       showCloseIcon: true,
    //       backgroundColor: errorColor,
    //       content: Text(response["message"]),
    //     ),
    //   );
    //   return;
    // } else {
    //   ScaffoldMessenger.of(context).showSnackBar(
    //     SnackBar(
    //       showCloseIcon: true,
    //       backgroundColor: successColor,
    //       content: Text("OTP sent successfully!"),
    //     ),
    //   );
    // }

    cooldown = Duration(seconds: 30);
    setState(() {});
    cooldownTimer = Timer.periodic(Duration(seconds: 1), tick);
  }

  focusField(BuildContext context) {
    if (!focusNode.hasFocus || MediaQuery.of(context).viewInsets.bottom > 0) {
      focusNode.requestFocus();
    } else {
      FocusScope.of(context).unfocus();
      Future.delayed(Duration(milliseconds: 10), () => focusNode.requestFocus());
    }
  }

  void tick(Timer t) {
    if (cooldown.inSeconds > 0) {
      cooldown -= Duration(seconds: 1);
      setState(() {});
    } else {
      cooldownTimer.cancel();
      setState(() {});
    }
  }
}
