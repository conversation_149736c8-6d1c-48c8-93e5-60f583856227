import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/view/screens/auth/otp_verify_screen.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/providers/auth_provider.dart';

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  TextEditingController emailController = TextEditingController(text: "<EMAIL>");
  TextEditingController passwordController = TextEditingController(text: "123456789");
  bool _isBusy = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(
            // vertical: MediaQuery.of(context).size.width / 20,
            horizontal: MediaQuery.of(context).size.width / 10,
          ),
          child: SizedBox(
            height: MediaQuery.of(context).size.height -
                MediaQuery.of(context).padding.top -
                MediaQuery.of(context).padding.bottom,
            child: Form(
              child: Column(
                children: [
                  Row(),
                  SizedBox(height: 50),
                  Image.asset(
                    "assets/images/logo.png",
                    width: MediaQuery.of(context).size.width * .3,
                    height: MediaQuery.of(context).size.width * .3,
                    color: primaryColor,
                  ),
                  SizedBox(height: 20),
                  Text(
                    "sign_in".tr,
                    style: Theme.of(context).primaryTextTheme.titleLarge,
                  ),
                  SizedBox(height: 8),
                  Text(
                    "welcome_back".tr,
                    style: TextStyle(
                      color: Color.fromARGB(255, 158, 158, 158),
                      fontSize: 16,
                    ),
                  ),
                  SizedBox(height: 24),
                  Row(
                    children: [
                      Text(
                        "email_address".tr,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  TextFormField(
                    controller: emailController,
                    textDirection: TextDirection.ltr,
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.emailAddress,
                    decoration: InputDecoration(
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(vertical: 8, horizontal: 10),
                      fillColor: Color.fromARGB(255, 245, 245, 245),
                      filled: true,
                      border: outlineInputBorder,
                      enabledBorder: outlineInputBorder,
                      focusedBorder: outlineInputBorder,
                      focusedErrorBorder: outlineInputBorder,
                      prefixIconConstraints: BoxConstraints(maxHeight: 26, maxWidth: 26),
                      prefixIcon: SizedBox(
                        child: Image.asset(
                          "assets/icons/email_filled.png",
                          color: greyColor,
                        ),
                      ),
                      hintText: "email_address".tr,
                      hintStyle:
                          Theme.of(context).textTheme.bodySmall!.copyWith(color: Colors.grey[500]),
                    ),
                  ),
                  SizedBox(height: 20),
                  Row(
                    children: [
                      Text(
                        "password".tr,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  TextFormField(
                    controller: passwordController,
                    textInputAction: TextInputAction.next,
                    textDirection: TextDirection.ltr,
                    obscureText: true,
                    decoration: InputDecoration(
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(vertical: 8, horizontal: 10),
                      fillColor: Color.fromARGB(255, 245, 245, 245),
                      filled: true,
                      border: outlineInputBorder,
                      enabledBorder: outlineInputBorder,
                      focusedBorder: outlineInputBorder,
                      focusedErrorBorder: outlineInputBorder,
                      prefixIconConstraints: BoxConstraints(maxHeight: 26, maxWidth: 26),
                      prefixIcon: SizedBox(
                        child: Image.asset(
                          "assets/icons/key_filled.png",
                          color: greyColor,
                        ),
                      ),
                      hintText: "#!&%@^\$&@%".tr,
                      hintStyle:
                          Theme.of(context).textTheme.bodySmall!.copyWith(color: Colors.grey[500]),
                    ),
                  ),
                  SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: _isBusy ? null : _login,
                    style: elevatedButtonStyle,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (_isBusy)
                          SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(),
                          )
                        else
                          Text("تسجيل الدخول"),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _login() async {
    if (_isBusy) return;
    setState(() {
      _isBusy = true;
    });

    String? checkInputError = _validateInput;
    if (checkInputError != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(checkInputError),
          backgroundColor: errorColor,
        ),
      );
      return;
    }
    String? loginError = await Provider.of<AuthProvider>(context, listen: false)
        .login(emailController.text, passwordController.text);

    if (!context.mounted) return;
    setState(() {
      _isBusy = false;
    });
    if (loginError != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(loginError),
          backgroundColor: errorColor,
        ),
      );
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => OtpVerifyScreen(email: emailController.text),
      ),
    );
  }

  String? get _validateInput {
    if (!emailController.text.validEmail) return "invalid_email";
    if (passwordController.text.isEmpty) return "invalid_password";
    return null;
  }
}
