import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_kit/models/bank_info_model.dart';
import 'package:hero_store_branch_app/providers/bank_info_provider.dart';
import 'package:hero_store_branch_app/view/screens/bank_accout/add_bank_account_screen.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_title.dart';
import 'package:hero_store_branch_app/view/widgets/bank_info_tile.dart';

class BankAccountInfoScreen extends StatelessWidget {
  const BankAccountInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: whiteColor,
      body: Column(
        children: [
          AppBarWithTitle(
            "bank_info".tr,
            backButton: true,
          ),
          Expanded(
            child: Consumer<BankInfoProvider>(
              builder: (context, bankProvider, child) {
                // Show loading indicator
                if (bankProvider.isLoading) {
                  return Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                    ),
                  );
                }

                // Show error message if there's an error
                if (bankProvider.errorMessage != null) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: errorColor.withValues(alpha: 0.1),
                          ),
                          child: Icon(
                            Icons.error_outline,
                            size: 48,
                            color: errorColor,
                          ),
                        ),
                        SizedBox(height: 24),
                        Text(
                          bankProvider.errorMessage!,
                          style: TextStyle(
                            color: errorColor,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 32),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () => bankProvider.refresh(),
                            style: elevatedButtonStyle.copyWith(
                              backgroundColor:
                                  WidgetStatePropertyAll(errorColor),
                              padding: WidgetStatePropertyAll(
                                EdgeInsets.symmetric(vertical: 16),
                              ),
                              shape: WidgetStatePropertyAll(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                            child: Text(
                              "retry".tr,
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                // Show empty state if no bank accounts
                if (bankProvider.bankInfoList.isEmpty) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Empty state illustration
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: primaryColor.withValues(alpha: 0.1),
                          ),
                          child: Center(
                            child: Image.asset(
                              "assets/icons/wallet_outline.png",
                              width: 48,
                              height: 48,
                              color: primaryColor,
                            ),
                          ),
                        ),
                        SizedBox(height: 24),
                        Text(
                          "add_bank_account_desciption".tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey[600],
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 48),
                        // Full-width red button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            style: elevatedButtonStyle.copyWith(
                              padding: WidgetStatePropertyAll(
                                EdgeInsets.symmetric(vertical: 16),
                              ),
                              shape: WidgetStatePropertyAll(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                            onPressed: () async {
                              BankInfoModel? toAdd = await Navigator.of(context)
                                  .push(MaterialPageRoute(
                                      builder: (_) => AddBankAccountScreen()));
                              if (toAdd == null || !context.mounted) {
                                return;
                              }

                              // Use the new API to create bank account
                              String? errorText =
                                  await bankProvider.createBankAccount(
                                bankId: toAdd.bankId,
                                bankBranchId: toAdd.bankBranchId,
                                personName: toAdd.personName,
                                accountNumber: toAdd.accountNumber,
                              );

                              if (errorText != null && context.mounted) {
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(SnackBar(
                                  content: Text(errorText),
                                  backgroundColor: errorColor,
                                ));
                              } else if (context.mounted) {
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(SnackBar(
                                  content: Text(
                                      "bank_account_created_successfully".tr),
                                  backgroundColor: successColor,
                                ));
                              }
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.add, size: 20),
                                SizedBox(width: 8),
                                Text(
                                  "add_bank_account".tr,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                // Show list of bank accounts
                return RefreshIndicator(
                  onRefresh: () => bankProvider.refresh(),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: ListView.builder(
                      itemCount: bankProvider.bankInfoList.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: EdgeInsets.only(bottom: 12),
                          child: BankInfoTile(bankProvider.bankInfoList[index]),
                        );
                      },
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: Consumer<BankInfoProvider>(
        builder: (context, bankProvider, child) {
          // Only show FAB when there are existing bank accounts
          if (bankProvider.bankInfoList.isNotEmpty && !bankProvider.isLoading) {
            return FloatingActionButton(
              onPressed: () async {
                BankInfoModel? toAdd = await Navigator.of(context).push(
                    MaterialPageRoute(builder: (_) => AddBankAccountScreen()));
                if (toAdd == null || !context.mounted) {
                  return;
                }

                String? errorText = await bankProvider.createBankAccount(
                  bankId: toAdd.bankId,
                  bankBranchId: toAdd.bankBranchId,
                  personName: toAdd.personName,
                  accountNumber: toAdd.accountNumber,
                );

                if (errorText != null && context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    content: Text(errorText),
                    backgroundColor: errorColor,
                  ));
                } else if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    content: Text("bank_account_created_successfully".tr),
                    backgroundColor: successColor,
                  ));
                }
              },
              backgroundColor: primaryColor,
              child: Icon(Icons.add, color: Colors.white),
            );
          }
          return SizedBox.shrink();
        },
      ),
    );
  }
}
