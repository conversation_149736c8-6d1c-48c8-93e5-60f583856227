import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_kit/models/bank_info_model.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_title.dart';
import 'package:hero_store_branch_app/view/widgets/label_with_padding.dart';

class AddBankAccountScreen extends StatelessWidget {
  const AddBankAccountScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final TextEditingController bankNameController = TextEditingController();
    final TextEditingController branchController = TextEditingController();
    final TextEditingController holderNameController = TextEditingController();
    final TextEditingController accountNoController = TextEditingController();
    final OutlineInputBorder outlinedInputBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(
        color: Colors.grey[300]!,
        width: .5,
      ),
    );
    late final InputDecoration inputDecoration = InputDecoration(
      isDense: true,
      contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 10),
      border: outlinedInputBorder,
      enabledBorder: outlinedInputBorder,
      focusedBorder: outlinedInputBorder,
      focusedErrorBorder: outlinedInputBorder,
      hintStyle: labelStyle.copyWith(color: Colors.grey[500]),
    );
    return Scaffold(
      body: Column(
        children: [
          AppBarWithTitle(
            "add_bank_account".tr,
            backButton: true,
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(
                vertical: 16,
                horizontal: 16,
              ),
              child: Form(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(),
                    LabelWithPadding("bank_name".tr),
                    TextFormField(
                      controller: bankNameController,
                      textInputAction: TextInputAction.next,
                      style: labelStyle,
                      decoration: inputDecoration.copyWith(
                        hintText: "bank_name".tr,
                      ),
                      keyboardType: TextInputType.name,
                    ),
                    LabelWithPadding("branch_name".tr),
                    TextFormField(
                      controller: branchController,
                      textInputAction: TextInputAction.next,
                      style: labelStyle,
                      decoration: inputDecoration.copyWith(
                        hintText: "branch_name".tr,
                      ),
                      keyboardType: TextInputType.name,
                    ),
                    LabelWithPadding("account_holder_name".tr),
                    TextFormField(
                      controller: holderNameController,
                      textInputAction: TextInputAction.next,
                      style: labelStyle,
                      decoration: inputDecoration.copyWith(
                        hintText: "account_holder_name".tr,
                      ),
                      keyboardType: TextInputType.name,
                    ),
                    LabelWithPadding("account_no".tr),
                    TextFormField(
                      controller: accountNoController,
                      textInputAction: TextInputAction.next,
                      style: labelStyle,
                      decoration: inputDecoration.copyWith(
                        hintText: "account_no".tr,
                      ),
                      keyboardType: TextInputType.text,
                    ),
                    SizedBox(
                      child: Padding(
                        padding: EdgeInsets.only(top: 16),
                        child: ElevatedButton(
                          style: elevatedButtonStyle,
                          onPressed: () {
                            Navigator.of(context).pop(
                              BankInfoModel(
                                id: 0, // Placeholder for new accounts
                                storeBranchId: 0, // Will be set by server
                                storeBranchIdName: "", // Will be set by server
                                bankId:
                                    1, // Default bank ID - should be selected by user
                                bankIdName: bankNameController.text,
                                bankBranchId:
                                    1, // Default branch ID - should be selected by user
                                bankBranchIdName: branchController.text,
                                personName: holderNameController.text,
                                accountNumber: accountNoController.text,
                              ),
                            );
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text("add".tr),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
