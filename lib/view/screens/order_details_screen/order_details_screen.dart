import 'package:flutter/material.dart';
import 'package:hero_kit/models/delivery_order.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/enums/order_status.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/providers/orders_provider.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_title.dart';
import 'package:hero_store_branch_app/view/widgets/cost_tile.dart';
import 'package:hero_store_branch_app/view/widgets/custom_divider.dart';
import 'package:hero_store_branch_app/view/widgets/customer_details_widget.dart';
import 'package:hero_store_branch_app/view/widgets/meal_tile.dart';
import 'package:hero_store_branch_app/view/widgets/order_tag.dart';
import 'package:hero_store_branch_app/view/widgets/order_timer.dart';

class OrderDetailsScreen extends StatelessWidget {
  const OrderDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Selector<OrdersProvider, DeliveryOrder?>(
          selector: (_, orderListProvider) => orderListProvider.currentOrderDetails,
          builder: (_, currentOrderDetails, __) {
            return Column(
              children: [
                AppBarWithTitle("order_details".tr, backButton: true),
                Expanded(
                  child: currentOrderDetails == null
                      ? Center(child: CircularProgressIndicator())
                      : SingleChildScrollView(
                          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    "${"order_number".tr}: ",
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 15,
                                    ),
                                  ),
                                  Text(
                                    "#${currentOrderDetails.id}",
                                    style: TextStyle(
                                      color: primaryColor,
                                      fontSize: 13,
                                    ),
                                  ),
                                ],
                              ),
                              CustomDivider(),
                              Text(
                                currentOrderDetails.createdAt.toString(),
                                textDirection: TextDirection.ltr,
                                style: TextStyle(color: primaryColor, fontSize: 11),
                              ),
                              CustomDivider(),
                              Row(
                                children: [
                                  OrderTag(text: currentOrderDetails.orderType.name.tr),
                                  SizedBox(width: 8),
                                  OrderTag(
                                      text: "${currentOrderDetails.products.length} ${"items".tr}"),
                                  SizedBox(width: 8),
                                  OrderTag(text: currentOrderDetails.paymentMethod.name.tr),
                                  Spacer(),
                                  currentOrderDetails.orderStatus.index >
                                          OrderStatus.executing.index
                                      ? SizedBox()
                                      : OrderTimer(
                                          orderStatus: currentOrderDetails.orderStatus,
                                          createdAt: currentOrderDetails.createdAt,
                                          confirmed: currentOrderDetails.executing,
                                          processingTime: currentOrderDetails.preparingTime,
                                          handover: currentOrderDetails.readyAt,
                                        ),
                                ],
                              ),
                              CustomDivider(),
                              SizedBox(height: 8),
                              Text(
                                "item_list".tr,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 8),
                              ...(currentOrderDetails.products).map((op) => MealTile(op)),
                              Text(
                                "customer_details".tr,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              CustomerDetailsWidget(
                                currentOrderDetails.deliveryOrderAddress,
                                currentOrderDetails.customerData,
                              ),
                              Text(
                                "cost".tr,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 12),
                              CostTile(
                                  title: "item_price".tr,
                                  cost: "${currentOrderDetails.orderAmount}"),
                              SizedBox(height: 8),
                              CostTile(
                                  title: "addons".tr, cost: "${currentOrderDetails.addonPrice}"),
                              CustomDivider(),
                              CostTile(
                                  title: "restaurant_total".tr,
                                  cost: "${currentOrderDetails.restaurantTotal}"),
                              SizedBox(height: 8),
                              CostTile(
                                  title: "delivery_charge".tr,
                                  cost: "${currentOrderDetails.deliveryCharge ?? 0}",
                                  active: false),
                              CustomDivider(),
                              CostTile(
                                  title: "discount_coupon".tr,
                                  cost: "${currentOrderDetails.couponDiscount}",
                                  disabled: currentOrderDetails.couponDiscount >= 0.0,
                                  active: false),
                              SizedBox(height: 8),
                              CostTile(
                                  title: "total_price".tr,
                                  cost: "${currentOrderDetails.orderAmount}"),
                              SizedBox(height: 24),
                              switch (currentOrderDetails.orderStatus) {
                                OrderStatus.pending => PendingControls(currentOrderDetails.id),
                                OrderStatus.executing => CookingControls(currentOrderDetails.id),
                                _ => SizedBox(),
                              },
                              SizedBox(height: 24),
                            ],
                          ),
                        ),
                ),
              ],
            );
          }),
    );
  }
}

class CookingControls extends StatelessWidget {
  final int orderID;
  const CookingControls(this.orderID, {super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: elevatedButtonStyle,
      onPressed: () async {
        bool confirmation = (await showDialog(
              context: context,
              builder: (context) => ConfirmationDialog(
                "change_order_status".tr,
                "change_to_ready_for_handover".tr,
                "confirm".tr,
                "cancel".tr,
              ),
            )) ??
            false;
        if (!context.mounted || !confirmation) {
          return;
        }
        String? errorMessage =
            await Provider.of<OrdersProvider>(context, listen: false).readyForHandover(orderID);
        if (context.mounted && errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text(errorMessage),
            backgroundColor: errorColor,
          ));
        }
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "handover".tr,
            style: TextStyle(fontSize: 13),
          ),
        ],
      ),
    );
  }
}

class PendingControls extends StatelessWidget {
  final int orderID;
  const PendingControls(this.orderID, {super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            style: elevatedButtonStyle,
            onPressed: () async {
              Duration? processingTime = await showDialog(
                barrierColor: Colors.grey.withAlpha(102),
                context: context,
                builder: (_) => SelectCookingTimeDialog(),
              );
              if (processingTime == null || !context.mounted) {
                return;
              }
              Provider.of<OrdersProvider>(context, listen: false)
                  .acceptOrder(orderID, processingTime);
            },
            child: Text(
              "accept_order".tr,
              style: TextStyle(fontSize: 13),
            ),
          ),
        ),
        SizedBox(width: 24),
        Expanded(
          child: ElevatedButton(
            style: elevatedCancelButtonStyle,
            onPressed: () async {
              String? reason = await showDialog(
                  context: context,
                  builder: (_) => AlertDialog(
                        content: Builder(builder: (context) {
                          TextEditingController reasonTEC = TextEditingController();
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    "rejection_reason".tr,
                                    style: TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.bold,
                                        color: primaryColor),
                                  ),
                                ],
                              ),
                              SizedBox(height: 16),
                              SizedBox(
                                height: 120,
                                child: TextFormField(
                                  controller: reasonTEC,
                                  decoration: InputDecoration(
                                      border: outlineInputBorder,
                                      fillColor: primaryColor.withAlpha(38),
                                      filled: true),
                                  maxLines: 5,
                                ),
                              ),
                              // SizedBox(height: 24),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: [
                                  TextButton(
                                    onPressed: () => Navigator.of(context).pop(),
                                    child: Text("cancel".tr),
                                  ),
                                  TextButton(
                                    onPressed: () => Navigator.of(context).pop(reasonTEC.text),
                                    child: Text("confirm".tr),
                                  ),
                                ],
                              )
                              // Row(
                              //   children: [
                              //     Expanded(
                              //         child: ElevatedButton(
                              //             style: elevatedButtonStyle,
                              //             onPressed: () {},
                              //             child: Row(
                              //               mainAxisAlignment: MainAxisAlignment.center,
                              //               children: [Text("ok".tr)],
                              //             ))),
                              //     SizedBox(width: 20),
                              //     Expanded(
                              //         child: ElevatedButton(
                              //             style: elevatedCancelButtonStyle,
                              //             onPressed: () {},
                              //             child: Row(
                              //               mainAxisAlignment: MainAxisAlignment.center,
                              //               children: [Text("cancel".tr)],
                              //             ))),
                              //   ],
                              // ),
                            ],
                          );
                        }),
                      ));
              if (reason == null || !context.mounted || reason.isEmpty) {
                return;
              }
              Provider.of<OrdersProvider>(context, listen: false)
                  .rejectOrder(orderID, reason: reason);
            },
            child: Text(
              "reject_order".tr,
              style: TextStyle(fontSize: 13),
            ),
          ),
        ),
      ],
    );
  }
}

class ConfirmationDialog extends StatelessWidget {
  final String title;
  final String text;
  final String accept;
  final String cancel;

  const ConfirmationDialog(this.title, this.text, this.accept, this.cancel, {super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(title),
      content: Text(text),
      actions: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                style: elevatedButtonStyle,
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(accept),
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                style: elevatedCancelButtonStyle,
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(cancel),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class SelectCookingTimeDialog extends StatefulWidget {
  const SelectCookingTimeDialog({super.key});

  @override
  State<SelectCookingTimeDialog> createState() => _SelectCookingTimeDialogState();
}

class _SelectCookingTimeDialogState extends State<SelectCookingTimeDialog> {
  final List<int> timerList = [15, 20, 25, 30, 35, 40];
  late int selectedValue = timerList.first;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Spacer(),
        Container(
          alignment: Alignment.bottomCenter,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.white,
          ),
          margin: EdgeInsets.all(16),
          padding: EdgeInsets.all(12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  "select_cooking_time".tr,
                  style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
                ),
              ),
              ...List.generate(
                (timerList.length / 3).ceil(),
                (r) => Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: List.generate(
                    timerList.length > (r * 3) + 2
                        ? 3
                        : timerList.length > (r * 3) + 1
                            ? 2
                            : 1,
                    (e) => TimeValueWidget(
                      value: timerList[(r * 3) + e],
                      active: selectedValue == timerList[(r * 3) + e],
                      onTap: () => setState(() {
                        selectedValue = timerList[(r * 3) + e];
                      }),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(Duration(minutes: selectedValue)),
                      style: elevatedButtonStyle,
                      child: Text("confirm".tr),
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: elevatedCancelButtonStyle,
                      child: Text("cancel".tr),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
            ],
          ),
        ),
      ],
    );
  }
}

class TimeValueWidget extends StatelessWidget {
  final int value;
  final bool active;
  final void Function()? onTap;
  const TimeValueWidget({
    required this.value,
    required this.active,
    required this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: active ? primaryColor : Colors.grey[400],
          borderRadius: BorderRadius.circular(8),
        ),
        padding: EdgeInsets.all(16),
        margin: EdgeInsets.all(8),
        child: Text(
          "$value",
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 16,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
