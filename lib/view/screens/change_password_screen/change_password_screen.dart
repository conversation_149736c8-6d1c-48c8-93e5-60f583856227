import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_title.dart';

class ChangePasswordScreen extends StatelessWidget {
  const ChangePasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Column(
        children: [
          AppBarWithTitle(
            "change_password".tr,
            backButton: true,
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 30),
              child: Column(
                children: [
                  Spacer(flex: 8),
                  TextFormField(
                    textInputAction: TextInputAction.next,
                    textDirection: TextDirection.ltr,
                    obscureText: true,
                    decoration: InputDecoration(
                      isDense: true,
                      contentPadding:
                          EdgeInsets.symmetric(vertical: 8, horizontal: 10),
                      fillColor: Color.fromARGB(255, 245, 245, 245),
                      filled: true,
                      border: outlineInputBorder,
                      enabledBorder: outlineInputBorder,
                      focusedBorder: outlineInputBorder,
                      focusedErrorBorder: outlineInputBorder,
                      prefixIcon: Icon(Icons.lock),
                      hintText: "current_password".tr,
                      hintStyle: Theme.of(context)
                          .textTheme
                          .bodySmall!
                          .copyWith(color: Colors.grey[500]),
                    ),
                  ),
                  SizedBox(height: 10),
                  TextFormField(
                    textInputAction: TextInputAction.next,
                    textDirection: TextDirection.ltr,
                    obscureText: true,
                    decoration: InputDecoration(
                      isDense: true,
                      contentPadding:
                          EdgeInsets.symmetric(vertical: 8, horizontal: 10),
                      fillColor: Color.fromARGB(255, 245, 245, 245),
                      filled: true,
                      border: outlineInputBorder,
                      enabledBorder: outlineInputBorder,
                      focusedBorder: outlineInputBorder,
                      focusedErrorBorder: outlineInputBorder,
                      prefixIcon: Icon(Icons.lock),
                      hintText: "new_password".tr,
                      hintStyle: Theme.of(context)
                          .textTheme
                          .bodySmall!
                          .copyWith(color: Colors.grey[500]),
                    ),
                  ),
                  SizedBox(height: 10),
                  TextFormField(
                    textInputAction: TextInputAction.next,
                    textDirection: TextDirection.ltr,
                    obscureText: true,
                    decoration: InputDecoration(
                      isDense: true,
                      contentPadding:
                          EdgeInsets.symmetric(vertical: 8, horizontal: 10),
                      fillColor: Color.fromARGB(255, 245, 245, 245),
                      filled: true,
                      border: outlineInputBorder,
                      enabledBorder: outlineInputBorder,
                      focusedBorder: outlineInputBorder,
                      focusedErrorBorder: outlineInputBorder,
                      prefixIcon: Icon(Icons.lock),
                      hintText: "new_password_approval".tr,
                      hintStyle: Theme.of(context)
                          .textTheme
                          .bodySmall!
                          .copyWith(color: Colors.grey[500]),
                    ),
                  ),
                  SizedBox(height: 10),
                  ElevatedButton(
                      style: elevatedButtonStyle,
                      onPressed: () {},
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text("save".tr),
                        ],
                      )),
                  Spacer(flex: 22),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
