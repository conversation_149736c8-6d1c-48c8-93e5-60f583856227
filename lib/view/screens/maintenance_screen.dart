import 'package:flutter/material.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_store_branch_app/providers/config_provider.dart';

class MaintenanceScreen extends StatefulWidget {
  const MaintenanceScreen({super.key});

  @override
  State<MaintenanceScreen> createState() => _MaintenanceScreenState();
}

class _MaintenanceScreenState extends State<MaintenanceScreen> {
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Spacer(flex: 2),
            // SizedBox(
            //   width: double.maxFinite,
            //   child: Image.asset(
            //     'assets/images/connectivity.png',
            //   ),
            // ),
            Text(
              'we are sorry',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                fontFamily: 'Din',
              ),
            ),
            Text(
              'we are experiencing some issues with our server, please try again later.',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                fontFamily: 'Din',
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 32),

            //try again button

            isLoading
                ? CircularProgressIndicator()
                : SizedBox(
                    width: double.maxFinite,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryColor,
                        foregroundColor: whiteColor,
                      ),
                      onPressed: () async {
                        setState(() {
                          isLoading = true;
                        });
                        await ConfigProvider().connectToServer();
                        setState(() {
                          isLoading = false;
                        });
                      },
                      child: Text("try_again".tr),
                    ),
                  ),
            Spacer(flex: 3),
          ],
        ),
      ),
    );
  }
}
