import 'package:flutter/material.dart';
import 'package:hero_kit/enums/store_branch_status.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/providers/store_branch_work_time_provider.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_title.dart';
import 'package:hero_store_branch_app/view/widgets/custom_toggle_button.dart';
import 'package:hero_store_branch_app/view/widgets/week_day_work_time_tile.dart';

class RestaurantOptionScreen extends StatelessWidget {
  const RestaurantOptionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Column(
        children: [
          AppBarWithTitle(
            "restaurant_data".tr,
            backButton: true,
          ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              children: [
                Text(
                  "restaurant_status".tr,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Si<PERSON><PERSON><PERSON>(height: 8),
                Container(
                  decoration: BoxDecoration(
                    color: primaryColor.withAlpha(38),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 6,
                  ),
                  height: 52,
                  child: Selector<StoreBranchWorkTimeProvider, StoreBranchStatus>(
                    selector: (_, worktimeProvider) => worktimeProvider.currentStatus,
                    builder: (_, cStatus, __) {
                      return Row(
                        children: StoreBranchStatus.values
                            .map(
                              (e) => CustomToggleButton(
                                e.name.tr,
                                active: cStatus == e,
                                onTap: () =>
                                    Provider.of<StoreBranchWorkTimeProvider>(context, listen: false)
                                        .setRestaurantStatus(e),
                              ),
                            )
                            .toList(),
                      );
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: Text(
                    "set_work_days_and_time".tr,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Container(
                  // height: 80,
                  // width: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: primaryColor.withAlpha(38),
                  ),
                  child: Column(
                    children: [
                      SizedBox(height: 4),
                      ...List.generate(7, (index) => index).map((i) => WeekDayWorkTimeTile(i)),
                      SizedBox(height: 4),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.all(16),
            child: ElevatedButton(
              onPressed: () {
                Provider.of<StoreBranchWorkTimeProvider>(context, listen: false).saveChanges();
                Navigator.of(context).pop();
              },
              style: elevatedButtonStyle,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text("save_changes".tr),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
