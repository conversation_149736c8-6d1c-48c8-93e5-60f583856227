import 'package:flutter/material.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_kit/models/store_branch_work_day.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_store_branch_app/models/hero_store_branch.dart';
import 'package:hero_store_branch_app/providers/auth_provider.dart';
import 'package:hero_store_branch_app/providers/printing_settings_provider.dart';
import 'package:hero_store_branch_app/providers/store_branch_work_time_provider.dart';
import 'package:provider/provider.dart';

class RestaurantDetailsScreen extends StatefulWidget {
  const RestaurantDetailsScreen({super.key});

  @override
  State<RestaurantDetailsScreen> createState() =>
      _RestaurantDetailsScreenState();
}

class _RestaurantDetailsScreenState extends State<RestaurantDetailsScreen> {
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadProfile();
    _loadPrintingSettings();
  }

  Future<void> _loadProfile() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final error = await AuthProvider().getProfile();
      if (error != null) {
        setState(() {
          errorMessage = error;
          isLoading = false;
        });
      } else {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = e.toString();
        isLoading = false;
      });
    }
  }

  Future<void> _loadPrintingSettings() async {
    final printingProvider = context.read<PrintingSettingsProvider>();
    await printingProvider.loadPrintingSettings();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: whiteColor,
      appBar: AppBar(
        backgroundColor: whiteColor,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        leading: Container(
          margin: EdgeInsets.only(left: 16, top: 8, bottom: 8),
          decoration: BoxDecoration(
            color: backgroundScreen,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: Icon(
              Icons.keyboard_arrow_right,
              color: primaryColor,
              size: 24,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        title: Text(
          "restaurant_data".tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: blackColor,
          ),
        ),
        centerTitle: true,
      ),
      body: isLoading
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
              ),
            )
          : errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: errorColor,
                      ),
                      SizedBox(height: 16),
                      Text(
                        errorMessage!,
                        style: TextStyle(
                          color: errorColor,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadProfile,
                        style: elevatedButtonStyle,
                        child: Text("update".tr),
                      ),
                    ],
                  ),
                )
              : Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    final storeBranch = authProvider.storeBranch;
                    final store = storeBranch.store;

                    return SingleChildScrollView(
                      child: Column(
                        children: [
                          // Restaurant Profile Section
                          Container(
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(vertical: 40),
                            child: Column(
                              children: [
                                // Restaurant Image
                                Container(
                                  width: 90,
                                  height: 90,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(48),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(48),
                                    child: Image.network(
                                      store.storeLogo,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) =>
                                              Image.asset(
                                        "assets/images/restaurant_default_image.png",
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(height: 4),

                                // Restaurant Name
                                Text(
                                  storeBranch.name,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: blackColor,
                                  ),
                                  textAlign: TextAlign.center,
                                ),

                                // Rating Stars
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: List.generate(5, (index) {
                                    return Container(
                                      width: 18,
                                      height: 18,
                                      margin:
                                          EdgeInsets.symmetric(horizontal: 4),
                                      child: Icon(
                                        Icons.star,
                                        size: 16,
                                        color:
                                            index < storeBranch.rating.floor()
                                                ? secondaryColor
                                                : Color(0xFFE7E5E5),
                                      ),
                                    );
                                  }),
                                ),

                                // Email
                                Text(
                                  store.name,
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: greyText,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),

                          // Working Hours Button
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 123),
                            child: ElevatedButton(
                              onPressed: () {
                                _showWorkingHoursBottomSheet(
                                    context, storeBranch);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: primaryColor,
                                foregroundColor: whiteColor,
                                padding: EdgeInsets.symmetric(
                                    vertical: 10, horizontal: 20),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30),
                                ),
                              ),
                              child: Text(
                                "restaurant_working_hours".tr,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(height: 97),

                          // Details Section
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 17),
                            child: Consumer<PrintingSettingsProvider>(
                              builder: (context, printingProvider, child) {
                                return Column(
                                  children: [
                                    _buildDetailItem(
                                      "restaurant_phone".tr,
                                      printingProvider.currentPhoneNumber,
                                    ),
                                    _buildDetailItem(
                                      "text_size".tr,
                                      printingProvider.getFontSizeDisplayText(
                                          printingProvider.currentFontSize),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
    );
  }

  Widget _buildDetailItem(String title, String value) {
    return GestureDetector(
      onTap: () {
        _showEditDialog(title, value);
      },
      child: SizedBox(
        height: 46,
        child: Column(
          children: [
            SizedBox(
              height: 45,
              child: Row(
                children: [
                  // Right arrow icon (pointing right)
                  Icon(
                    Icons.keyboard_arrow_right,
                    color: Color(0xFF8F181D),
                    size: 20,
                  ),
                  Spacer(),
                  // Title text (right-aligned)
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: blackColor,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ],
              ),
            ),
            // Divider line
            Container(
              height: 1,
              color: Color(0xFFF5F5F5),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditDialog(String title, String currentValue) {
    if (title == "text_size".tr) {
      _showFontSizeDialog();
    } else {
      _showPhoneNumberDialog(currentValue);
    }
  }

  void _showPhoneNumberDialog(String currentValue) {
    // Only set text if there's an actual phone number, not the placeholder text
    TextEditingController controller =
        TextEditingController(text: currentValue);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          "restaurant_phone".tr,
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 16),
        ),
        content: TextField(
          controller: controller,
          textAlign: TextAlign.right,
          keyboardType: TextInputType.phone,
          decoration: InputDecoration(
            border: OutlineInputBorder(),
            hintText: "أدخل رقم الهاتف",
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text("cancel_action".tr),
          ),
          TextButton(
            onPressed: () async {
              final printingProvider = context.read<PrintingSettingsProvider>();
              final success = await printingProvider.updatePrintingSettings(
                printMobileNumber: controller.text,
              );

              Navigator.pop(context);

              if (success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text("تم حفظ رقم الهاتف بنجاح"),
                    backgroundColor: successColor,
                  ),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text("فشل في حفظ رقم الهاتف"),
                    backgroundColor: errorColor,
                  ),
                );
              }
            },
            child: Text("save_hours".tr),
          ),
        ],
      ),
    );
  }

  void _showFontSizeDialog() {
    final printingProvider = context.read<PrintingSettingsProvider>();
    String selectedSize = printingProvider.currentFontSize;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          "text_size".tr,
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: printingProvider.fontSizeOptions.map((option) {
            return RadioListTile<String>(
              title: Text(
                option["display"]!,
                textAlign: TextAlign.right,
              ),
              value: option["value"]!,
              groupValue: selectedSize,
              onChanged: (value) {
                selectedSize = value!;
                Navigator.pop(context);
                _updateFontSize(selectedSize);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text("cancel_action".tr),
          ),
        ],
      ),
    );
  }

  Future<void> _updateFontSize(String fontSize) async {
    final printingProvider = context.read<PrintingSettingsProvider>();
    final success = await printingProvider.updatePrintingSettings(
      printFontSize: fontSize,
    );

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("تم حفظ حجم الخط بنجاح"),
          backgroundColor: successColor,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("فشل في حفظ حجم الخط"),
          backgroundColor: errorColor,
        ),
      );
    }
  }

  void _showWorkingHoursBottomSheet(
      BuildContext context, HeroStoreBranch storeBranch) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Color(0x669E9E9E), // Semi-transparent overlay
      builder: (context) => ChangeNotifierProvider(
        create: (context) =>
            StoreBranchWorkTimeProvider.from(authProvider: AuthProvider()),
        child: WorkingHoursBottomSheet(),
      ),
    );
  }
}

class WorkingHoursBottomSheet extends StatelessWidget {
  const WorkingHoursBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.56,
      decoration: BoxDecoration(
        color: whiteColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: EdgeInsets.only(top: 25),
            width: 134,
            height: 5,
            decoration: BoxDecoration(
              color: greyText,
              borderRadius: BorderRadius.circular(100),
            ),
          ),

          // Header
          Container(
            margin: EdgeInsets.only(top: 28),
            child: Text(
              "working_hours_app".tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: blackColor,
              ),
            ),
          ),

          // Working hours list
          Expanded(
            child: Consumer<StoreBranchWorkTimeProvider>(
              builder: (context, provider, child) {
                // Ensure we have work days for all 7 days
                _ensureWorkDays(provider);

                return Container(
                  margin: EdgeInsets.only(top: 66),
                  child: Column(
                    children: List.generate(7, (index) {
                      // Find the work day with the matching day index
                      final workDay = provider.workDays.firstWhere(
                        (wd) => wd.day == index,
                        orElse: () => StoreBranchWorkDay(
                          day: index,
                          working: false,
                          openingTime: TimeOfDay(hour: 12, minute: 0),
                          closingTime: TimeOfDay(hour: 20, minute: 0),
                        ),
                      );
                      final dayName = _getDayName(index);

                      return GestureDetector(
                        onTap: () =>
                            _showTimePickerDialog(context, provider, index),
                        child: Column(
                          children: [
                            Container(
                              height: 46,
                              padding: EdgeInsets.symmetric(horizontal: 34),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  // Time display (left-aligned)
                                  Text(
                                    workDay.working
                                        ? "${_formatTime(workDay.openingTime)} - ${_formatTime(workDay.closingTime)}"
                                        : "12:00 - 8:00",
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF161616),
                                      fontWeight: FontWeight.w600,
                                    ),
                                    textAlign: TextAlign.right,
                                  ),

                                  // Day name (right-aligned)
                                  Text(
                                    dayName,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF161616),
                                      fontWeight: FontWeight.w600,
                                    ),
                                    textAlign: TextAlign.right,
                                  ),
                                ],
                              ),
                            ),
                            // Divider line (except for last item)
                            if (index < 6)
                              Container(
                                height: 1,
                                color: Color(0xFFF5F5F5),
                                margin: EdgeInsets.symmetric(horizontal: 34),
                              ),
                          ],
                        ),
                      );
                    }),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showTimePickerDialog(BuildContext context,
      StoreBranchWorkTimeProvider provider, int dayIndex) {
    // Find the work day with this day index
    final workDayIndex =
        provider.workDays.indexWhere((wd) => wd.day == dayIndex);

    // If no work day exists for this index, create one
    if (workDayIndex == -1) {
      _createWorkDay(provider, dayIndex, false);
    }

    // Get the work day (which now definitely exists)
    final int updatedIndex =
        provider.workDays.indexWhere((wd) => wd.day == dayIndex);
    final workDay = provider.workDays[updatedIndex];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          "edit_working_hours".tr + " - ${_getDayName(dayIndex)}",
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Working toggle
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Switch(
                  value: workDay.working,
                  onChanged: (value) {
                    provider.setWorking(dayIndex, value);
                    Navigator.pop(context);
                  },
                  activeColor: primaryColor,
                ),
                Text(
                  workDay.working ? "working".tr : "closed".tr,
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),

            if (workDay.working) ...[
              SizedBox(height: 16),

              // Opening time
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ElevatedButton(
                    onPressed: () async {
                      final time = await showTimePicker(
                        context: context,
                        initialTime: workDay.openingTime,
                      );

                      if (time != null) {
                        provider.setStartTime(time, dayIndex);
                        Navigator.pop(context);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: backgroundScreen,
                      foregroundColor: blackColor,
                    ),
                    child: Text(_formatTime(workDay.openingTime)),
                  ),
                  Text("opening_time".tr),
                ],
              ),

              SizedBox(height: 12),

              // Closing time
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ElevatedButton(
                    onPressed: () async {
                      final time = await showTimePicker(
                        context: context,
                        initialTime: workDay.closingTime,
                      );

                      if (time != null) {
                        provider.setEndTime(time, dayIndex);
                        Navigator.pop(context);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: backgroundScreen,
                      foregroundColor: blackColor,
                    ),
                    child: Text(_formatTime(workDay.closingTime)),
                  ),
                  Text("closing_time".tr),
                ],
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text("cancel_action".tr),
          ),
        ],
      ),
    );
  }

  void _ensureWorkDays(StoreBranchWorkTimeProvider provider) {
    // If we don't have enough work days, create default ones
    for (int dayIndex = 0; dayIndex < 7; dayIndex++) {
      // Check if we already have a work day for this index
      final existingDayIndex =
          provider.workDays.indexWhere((workDay) => workDay.day == dayIndex);

      if (existingDayIndex == -1) {
        // No work day exists for this day index, create one
        final newWorkDay = StoreBranchWorkDay(
          day: dayIndex, // Use the integer day index (0-6)
          working: false,
          openingTime: TimeOfDay(hour: 9, minute: 0),
          closingTime: TimeOfDay(hour: 18, minute: 0),
        );
        provider.workDays.add(newWorkDay);
      }
    }

    // Sort the work days by day index to ensure they're in order
    provider.workDays.sort((a, b) => a.day.compareTo(b.day));
  }

  void _createWorkDay(
      StoreBranchWorkTimeProvider provider, int dayIndex, bool working,
      {TimeOfDay? openingTime, TimeOfDay? closingTime}) {
    // Find the work day for this day index
    int existingIndex =
        provider.workDays.indexWhere((workDay) => workDay.day == dayIndex);

    if (existingIndex == -1) {
      // No work day exists for this day index, create one
      final newWorkDay = StoreBranchWorkDay(
        day: dayIndex, // Use the integer day index (0-6)
        working: working,
        openingTime: openingTime ?? TimeOfDay(hour: 9, minute: 0),
        closingTime: closingTime ?? TimeOfDay(hour: 18, minute: 0),
      );
      provider.workDays.add(newWorkDay);

      // Sort the work days by day index
      provider.workDays.sort((a, b) => a.day.compareTo(b.day));

      // Find the index of our newly added day
      existingIndex =
          provider.workDays.indexWhere((workDay) => workDay.day == dayIndex);
    } else {
      // Update the existing work day
      final workDay = provider.workDays[existingIndex];
      workDay.working = working;
      if (openingTime != null) workDay.openingTime = openingTime;
      if (closingTime != null) workDay.closingTime = closingTime;
    }
  }

  String _getDayName(int index) {
    const dayNames = [
      "الأحد",
      "الإثنين",
      "الثلاثاء",
      "الأربعاء",
      "الخميس",
      "الجمعة",
      "السبت",
    ];
    return dayNames[index % 7];
  }

  String _formatTime(TimeOfDay? time) {
    if (time == null) return "00:00";
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return "$hour:$minute";
  }
}
