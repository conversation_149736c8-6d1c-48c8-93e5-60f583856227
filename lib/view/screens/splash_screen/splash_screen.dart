import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/consts.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: primaryColor.with<PERSON><PERSON><PERSON>(38),
        child: <PERSON><PERSON><PERSON>(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(),
            Image.asset(
              "assets/images/logo.png",
              width: MediaQuery.of(context).size.width * .4,
              height: MediaQuery.of(context).size.width * .4,
              color: primaryColor,
            ),
            SizedBox(height: MediaQuery.of(context).size.width * .21),
          ],
        ),
      ),
    );
  }
}
