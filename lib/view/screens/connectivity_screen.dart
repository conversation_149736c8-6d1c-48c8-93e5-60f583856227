import 'package:flutter/material.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_store_branch_app/providers/config_provider.dart';

class ConnectivityScreen extends StatefulWidget {
  const ConnectivityScreen({super.key});

  @override
  State<ConnectivityScreen> createState() => _ConnectivityScreenState();
}

class _ConnectivityScreenState extends State<ConnectivityScreen> {
  bool isLoading = false;
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Spacer(flex: 2),
            // SizedBox(
            //   width: double.maxFinite,
            //   child: Image.asset(
            //     'assets/images/connectivity.png',
            //     fit: BoxFit.fitWidth,
            //   ),
            // ),
            SizedBox(height: 24),
            Text(
              'You are offline',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                fontFamily: 'Din',
              ),
            ),
            Text(
              'please check your internet connection and try again',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                fontFamily: 'Din',
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 32),

            //try again button

            SizedBox(
              height: 50,
              child: Column(
                children: [
                  isLoading
                      ? CircularProgressIndicator()
                      : SizedBox(
                          width: double.maxFinite,
                          child: ElevatedButton(
                            onPressed: () async {
                              setState(() {
                                isLoading = true;
                              });
                              await ConfigProvider().connectToServer();
                              setState(() {
                                isLoading = false;
                              });
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: primaryColor,
                              foregroundColor: whiteColor,
                            ),
                            child: Text("try_again".tr),
                          ),
                        )
                ],
              ),
            ),
            Spacer(flex: 3),
          ],
        ),
      ),
    );
  }
}
