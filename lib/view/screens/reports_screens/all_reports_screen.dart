import 'package:flutter/material.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_title.dart';

class AllReportsScreen extends StatelessWidget {
  const AllReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          AppBarWithTitle(
            "reports".tr,
            backButton: true,
          ),
          Expanded(
            child: ListView(),
          ),
        ],
      ),
    );
  }
}
