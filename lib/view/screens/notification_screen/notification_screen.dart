import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hero_kit/extensions/string_extension.dart';
import 'package:hero_store_branch_app/models/notification_model.dart';
import 'package:hero_store_branch_app/providers/notification_provider.dart';
import 'package:hero_store_branch_app/view/widgets/app_bar_with_title.dart';
import 'package:hero_store_branch_app/view/widgets/notification_tile.dart';

class NotificationScreen extends StatelessWidget {
  const NotificationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          AppBarWithTitle(
            "notifications".tr,
            backButton: true,
          ),
          Expanded(
            child: Selector<NotificationProvider, List<NotificationModel>>(
                selector: (context, notificationProvider) => notificationProvider.notificationList,
                builder: (_, notificationList, __) {
                  if (notificationList.isEmpty) {
                    return Center(
                      child: CircularProgressIndicator(),
                    );
                  }
                  return ListView(
                    padding: EdgeInsets.symmetric(vertical: 8),
                    children: notificationList
                        .map(
                          (n) => NotificationTile(
                            n,
                            onTap: () {},
                          ),
                        )
                        .toList(),
                  );
                }),
          ),
        ],
      ),
    );
  }
}
