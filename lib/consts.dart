import 'package:flutter/material.dart';
// import 'package:hero_kit/enums/logger_enums.dart';

final ThemeData lightThemeData = ThemeData();
final ThemeData darkThemeData = ThemeData();

final OutlineInputBorder outlineInputBorder = OutlineInputBorder(
  borderRadius: BorderRadius.circular(8),
  // borderSide: BorderSide(
  //   color: Colors.grey[400]!,
  // ),
  borderSide: BorderSide.none,
);

final ButtonStyle elevatedButtonStyle = ElevatedButton.styleFrom(
  padding: EdgeInsets.symmetric(vertical: 14),
  elevation: 0,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(8),
  ),
  backgroundColor: primaryColor,
  foregroundColor: Colors.white,
);
final ButtonStyle elevatedCancelButtonStyle = ElevatedButton.styleFrom(
  padding: EdgeInsets.symmetric(vertical: 14),
  elevation: 0,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(8),
  ),
  backgroundColor: Colors.grey,
  foregroundColor: Colors.white,
);

const TextStyle labelStyle = TextStyle(
  fontWeight: FontWeight.w600,
  fontSize: 13,
);

const int fixedMinAlert = 3;

const String iosUri = 'https://apps.apple.com/app/id6444494402';
const String androidUri = 'https://play.google.com/store/apps/details?id=otlub.user.app';

const Color unimplementedColor = Color(0xFFFF00FF);
const MaterialColor primaryColor = MaterialColor(
  0xFFBE2026,
  <int, Color>{
    50: Color(0xFFF9E9E9),
    100: Color(0xFFF5DEDE), //lightHover
    200: Color(0xFFEBBABC), //lightActive
    300: Color(0xFFBE2026),
    400: Color(0xFFAB1D22),
    500: Color(0xFF981A1E), //normalActive
    600: Color(0xFF8F181D),
    700: Color(0xFF721317), //darkHover
    800: Color(0xFF550E11),
    900: Color(0xFF430B0D),
  },
);
const MaterialColor secondaryColor = MaterialColor(
  0xFFF57C00,
  <int, Color>{
    50: Color(0xFFFEF2E6), //light
    100: Color(0xFFFEEBD9), //lightHover
    200: Color(0xFFFCD6B0), //lightActive
    300: Color(0xFFF57C00), //normal
    400: Color(0xFFDD7000), //normalHover
    500: Color(0xFFC46300), //normalActive
    600: Color(0xFFB85D00), //dark
    700: Color(0xFF934A00), //darkHover
    800: Color(0xFF6E3800), //darkActive
    900: Color(0xFF562B00), //darker
  },
);

const Color backgroundText = Color(0xFFF5F5F5);
const Color fillLabelsColor = Color(0xFFFAF5F5);
const Color greyColor = Color(0xFF9E9E9E);
const Color errorColor = Color(0xFFE53935);
const Color darkGray = Color(0xFF424242);
const Color successColor = Color(0xFF4CAF50);
const Color bordersColor = Color(0xFFE7E5E5);
const Color blackColor = Color(0xFF0A2533);
const Color whiteColor = Color(0xFFFFFFFF);
const Color greyText = Color(0xFF9796A1);
const Color dropShadow = Color(0x22B4BCC9);
const Color backgroundScreen = Color(0xFFFDF8F8);
const Color transparentColor = Colors.transparent;
const Color circularBorderColor = Color(0xFFE0E0E0);

//RANDOM SHIT.....
const Color imageFilterColor = Color(0x46797979);
const Color lightCardColor = Color(0xFFEEF5FB);

const Color shadowFill = Color(0xCC323233);
const Color profileIconBg = Color(0xFFF2F9FC);
const Color multiShadowNotNeeded = Color(0x46000000);

const String currency = "₪";
const String currencyName = "shekel";
const int appVersion = 1;

extension MaterialColorExtension on MaterialColor {
  Color get light => this[50] ?? unimplementedColor;
  Color get lightHover => this[100] ?? unimplementedColor;
  Color get lightActive => this[200] ?? unimplementedColor;
  Color get normal => this[300] ?? unimplementedColor;
  Color get normalHover => this[400] ?? unimplementedColor;
  Color get normalActive => this[500] ?? unimplementedColor;
  Color get dark => this[600] ?? unimplementedColor;
  Color get darkHover => this[700] ?? unimplementedColor;
  Color get darkActive => this[800] ?? unimplementedColor;
  Color get darker => this[900] ?? unimplementedColor;
}
