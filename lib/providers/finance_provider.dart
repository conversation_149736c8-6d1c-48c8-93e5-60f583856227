import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/data/finance_repo.dart';
import 'package:hero_store_branch_app/models/financial_summary_model.dart';

class FinanceProvider with ChangeNotifier {
  final FinanceRepo _financeRepo = FinanceRepo();

  FinancialSummaryModel? _financialSummary;
  bool _isLoading = false;
  String? _error;

  // Report type values: Daily, Weekly, Monthly, Yearly, Custom
  String _selectedReportType = 'Daily';
  DateTime _startDate = DateTime.now();
  DateTime _endDate = DateTime.now();

  // Getters
  FinancialSummaryModel? get financialSummary => _financialSummary;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get selectedReportType => _selectedReportType;
  DateTime get startDate => _startDate;
  DateTime get endDate => _endDate;

  List<String> get reportTypes =>
      ['Daily', 'Weekly', 'Monthly', 'Yearly', 'Custom'];

  void setReportType(String reportType) {
    _selectedReportType = reportType;

    // Set default date ranges for non-custom types
    if (reportType != 'Custom') {
      DateTime now = DateTime.now();
      switch (reportType) {
        case 'Daily':
          _startDate = now;
          _endDate = now;
          break;
        case 'Weekly':
          _startDate = now.subtract(Duration(days: now.weekday - 1));
          _endDate = now.add(Duration(days: 7 - now.weekday));
          break;
        case 'Monthly':
          _startDate = DateTime(now.year, now.month, 1);
          _endDate = DateTime(now.year, now.month + 1, 0);
          break;
        case 'Yearly':
          _startDate = DateTime(now.year, 1, 1);
          _endDate = DateTime(now.year, 12, 31);
          break;
      }
    }
    notifyListeners();

    // Auto-load data when report type changes
    loadFinancialSummary();
  }

  void setDateRange(DateTime start, DateTime end) {
    _startDate = start;
    _endDate = end;
    notifyListeners();
    // Auto-load data when date range changes
    loadFinancialSummary();
  }

  Future<void> loadFinancialSummary() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      String startDateStr = _formatDate(_startDate);
      String endDateStr = _formatDate(_endDate);

      Map<String, dynamic> result =
          await _financeRepo.getFinancialSummaryReport(
        reportType: _selectedReportType,
        startDate: startDateStr,
        endDate: endDateStr,
      );

      if (result["result"] == true) {
        _financialSummary = result["data"] as FinancialSummaryModel;
        _error = null;
      } else {
        _error = result["reasonPhrase"] ?? "Failed to load financial data";
        _financialSummary = null;
      }
    } catch (e) {
      _error = "An error occurred: $e";
      _financialSummary = null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  String _formatDate(DateTime date) {
    return "${date.year.toString().padLeft(4, '0')}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  void refresh() {
    loadFinancialSummary();
  }
}
