import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/data/finance_repo.dart';
import 'package:hero_kit/models/bank_info_model.dart';

class BankInfoProvider with ChangeNotifier {
  List<BankInfoModel> bankInfoList = [];
  bool isLoading = false;
  String? errorMessage;

  BankInfoProvider() {
    // Auto-load bank accounts when provider is created
    loadBankAccounts();
  }

  /// Load all bank accounts for the store branch
  Future<void> loadBankAccounts() async {
    isLoading = true;
    errorMessage = null;
    notifyListeners();

    try {
      Map<String, dynamic> result = await FinanceRepo().getAllBankAccounts();
      if (result["result"]) {
        // Parse the bank accounts from the response
        if (result["body"]["data"] != null && result["body"]["data"] is List) {
          bankInfoList = (result["body"]["data"] as List)
              .map((bankData) => BankInfoModel.fromJson(bankData))
              .toList();
        } else {
          bankInfoList = [];
        }
      } else {
        errorMessage =
            result["body"]["message"] ?? "Failed to load bank accounts";
        bankInfoList = [];
      }
    } catch (e) {
      errorMessage = "An error occurred while loading bank accounts";
      bankInfoList = [];
    }

    isLoading = false;
    notifyListeners();
  }

  /// Create a new bank account
  Future<String?> createBankAccount({
    required int bankId,
    required int bankBranchId,
    required String personName,
    required String accountNumber,
  }) async {
    isLoading = true;
    errorMessage = null;
    notifyListeners();

    try {
      Map<String, dynamic> result = await FinanceRepo().createBankAccount(
        bankId: bankId,
        bankBranchId: bankBranchId,
        personName: personName,
        accountNumber: accountNumber,
      );

      if (result["result"]) {
        // Reload the bank accounts list to get the updated data
        await loadBankAccounts();
        return null; // Success
      } else {
        errorMessage =
            result["body"]["message"] ?? "Failed to create bank account";
        isLoading = false;
        notifyListeners();
        return errorMessage;
      }
    } catch (e) {
      errorMessage = "An error occurred while creating the bank account";
      isLoading = false;
      notifyListeners();
      return errorMessage;
    }
  }

  /// Update an existing bank account
  Future<String?> updateBankAccount({
    required int bankAccountId,
    required int bankId,
    required int bankBranchId,
    required String personName,
    required String accountNumber,
  }) async {
    isLoading = true;
    errorMessage = null;
    notifyListeners();

    try {
      Map<String, dynamic> result = await FinanceRepo().updateBankAccount(
        bankAccountId: bankAccountId,
        bankId: bankId,
        bankBranchId: bankBranchId,
        personName: personName,
        accountNumber: accountNumber,
      );

      if (result["result"]) {
        // Reload the bank accounts list to get the updated data
        await loadBankAccounts();
        return null; // Success
      } else {
        errorMessage =
            result["body"]["message"] ?? "Failed to update bank account";
        isLoading = false;
        notifyListeners();
        return errorMessage;
      }
    } catch (e) {
      errorMessage = "An error occurred while updating the bank account";
      isLoading = false;
      notifyListeners();
      return errorMessage;
    }
  }

  /// Delete a bank account
  Future<String?> deleteBankAccount(int bankAccountId) async {
    isLoading = true;
    errorMessage = null;
    notifyListeners();

    try {
      Map<String, dynamic> result =
          await FinanceRepo().deleteBankAccount(bankAccountId);

      if (result["result"]) {
        // Reload the bank accounts list to get the updated data
        await loadBankAccounts();
        return null; // Success
      } else {
        errorMessage =
            result["body"]["message"] ?? "Failed to delete bank account";
        isLoading = false;
        notifyListeners();
        return errorMessage;
      }
    } catch (e) {
      errorMessage = "An error occurred while deleting the bank account";
      isLoading = false;
      notifyListeners();
      return errorMessage;
    }
  }

  /// Get bank branches for a specific bank
  Future<List<dynamic>?> getBankBranches(int bankId) async {
    try {
      Map<String, dynamic> result = await FinanceRepo().getBankBranches(bankId);
      if (result["result"]) {
        return result["body"]["data"] as List<dynamic>?;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Legacy method - kept for backward compatibility
  @Deprecated(
      'Use createBankAccount instead. This method will be removed in a future version.')
  Future<String?> addBankInfo(BankInfoModel bankInfo) async {
    isLoading = true;
    notifyListeners();

    Map<String, dynamic> result = await FinanceRepo().addBankAccount(bankInfo);
    if (!result["result"]) {
      isLoading = false;
      notifyListeners();
      return result["body"]["errors"][0]["message"];
    }

    // Reload accounts after adding
    await loadBankAccounts();
    return null;
  }

  /// Refresh the bank accounts list
  Future<void> refresh() async {
    await loadBankAccounts();
  }

  /// Clear error message
  void clearError() {
    errorMessage = null;
    notifyListeners();
  }
}
