import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:hero_kit/enums/order_filter.dart';
import 'package:hero_kit/enums/order_status.dart';
import 'package:hero_kit/models/bond.dart';
import 'package:hero_kit/models/delivery_order.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_store_branch_app/data/order_repo.dart';
import 'package:hero_store_branch_app/otlub_vendor_app.dart';
import 'package:hero_store_branch_app/view/widgets/new_request_dialog_widget.dart';

class OrdersProvider with ChangeNotifier {
  String selectedStatusFilter = "pending";
  OrderFilter selectedOrderFilter = OrderFilter.delivered;
  bool ticker = false;
  List<DeliveryOrder> runningOrderList = [];
  List<DeliveryOrder> completeOrderList = [];
  List<Bond> completeBondList = [];
  late final Timer tt;
  late int _duration = _fixedMinAlert * 60;
  final int _fixedMinAlert = fixedMinAlert;
  DeliveryOrder? currentOrderDetails;
  bool alertShown = false;
  Map<String, int> orderCounters = {
    "pending_count": 0,
    "executing_count": 0,
    "ready_count": 0,
    "rejected_count": 0
  };
  String selectedCompleteOrderPeriod = "daily";
  int runningOrderPage = 1;
  int completeOrderPage = 1;
  DateTime? periodFilterStartDate;
  DateTime? periodFilterEndDate;

  void tick(Timer t) {
    _duration++;
    if (runningOrderList.isEmpty) {
      return;
    }
    ticker = !ticker;
    if (runningOrderList
            .where((o) => o.orderStatus == OrderStatus.pending)
            .isNotEmpty &&
        !alertShown &&
        _duration > 60 * _fixedMinAlert) {
      _duration = 0;
      alertShown = true;
      navigatorKey.dialog(builder: (_) => NewRequestDialogWidget());
    }
    notifyListeners();
  }

  void selectStatusFilter(String orderStatus) {
    if (orderStatus == selectedStatusFilter) return;
    selectedStatusFilter = orderStatus;
    runningOrderList = List.empty();
    runningOrderPage = 1;
    notifyListeners();
    refreshCurrentOrders(orderStatus);
  }

  void selectOrderFilter(OrderFilter newOrderFilter) {
    if (selectedOrderFilter == newOrderFilter) return;
    selectedOrderFilter = newOrderFilter;
    completeBondList = List.empty();
    completeOrderList = List.empty();
    refreshCompleteOrders(selectedOrderFilter, selectedCompleteOrderPeriod);
    notifyListeners();
  }

  void selectCompleteOrderPeriod(String newPeriod) {
    if (selectedCompleteOrderPeriod == newPeriod) return;
    selectedCompleteOrderPeriod = newPeriod;
    completeBondList = List.empty();
    completeOrderList = List.empty();
    refreshCompleteOrders(selectedOrderFilter, selectedCompleteOrderPeriod);
    notifyListeners();
  }

  Future<String?> acceptOrder(int orderID, Duration processingTime) async {
    Map<String, dynamic> result = await OrderRepo().updateOrderStatus(
      orderID: orderID,
      newOrderStatus: OrderStatus.accepted,
    );
    if (!result["result"]) {
      return result["body"]["errors"][0]["message"];
    }
    result = await OrderRepo().updateOrderStatus(
      orderID: orderID,
      newOrderStatus: OrderStatus.executing,
      processingTime: processingTime.inMinutes,
    );
    if (!result["result"]) {
      return result["body"]["errors"][0]["message"];
    }
    // refreshList();
    return null;
  }

  Future<String?> rejectOrder(int orderID, {required String reason}) async {
    Map<String, dynamic> result = await OrderRepo().updateOrderStatus(
      orderID: orderID,
      newOrderStatus: OrderStatus.cancelled,
      reason: reason,
    );
    if (!result["result"]) {
      return result["body"]["errors"][0]["message"];
    }
    // refreshList();
    return null;
  }

  Future<String?> readyForHandover(int orderID) async {
    Map<String, dynamic> result = await OrderRepo().updateOrderStatus(
      orderID: orderID,
      newOrderStatus: OrderStatus.readyToDelivery,
    );
    if (!result["result"]) {
      return result["body"]["errors"][0]["message"];
    }
    // refreshList();
    return null;
  }

  Future<void> refreshCurrentOrders(String orderStatus,
      {String stage = 'pending'}) async {
    Map<String, dynamic> result = await OrderRepo().getCurrentOrders(
      orderStatus,
      offset: (runningOrderPage - 1) * 10,
      stage: stage,
    );
    final body = result["body"];
    runningOrderList = List.from((body["data"]['orders'] as List)
        .map(
          (e) => DeliveryOrder.fromMap(e),
        )
        .toList());
    notifyListeners();
  }

  Future<void> refreshCompleteOrders(
      OrderFilter orderFilter, String period) async {
    //TODO fetch
    // await OrderRepo().getCompleteOrders();
  }

  Future<void> refreshList() async {
    await refreshCurrentOrders("pending");
    await refreshCompleteOrders(OrderFilter.delivered, "daily");

    // List<Future<void>> refreshLists = [
    //   refreshCurrentOrders(),
    //   refreshCompleteOrders(),
    // ];
    // await Future.wait(refreshLists);

    if (currentOrderDetails != null) {
      getOrderDetails(currentOrderDetails!.id);
    }
  }

  Future<void> refreshCounters() async {
    Map<String, dynamic> result = await OrderRepo().getstatistics();
    if (result["result"]) {
      orderCounters = Map.from(result["body"]["data"]);
      notifyListeners();
    }
  }

  Future<void> getOrderDetails(int id) async {
    Map<String, dynamic> result =
        await OrderRepo().getOrderDetails(orderID: id);
    currentOrderDetails = DeliveryOrder.fromMap(result["body"]["order"]);
    notifyListeners();
  }

  void clearOrderDetails() {
    currentOrderDetails = null;
    notifyListeners();
  }

  OrdersProvider() {
    tt = Timer.periodic(Duration(seconds: 1), tick);
    refreshList();
    refreshCounters();
  }

  void runningOrdersBack() {
    if (runningOrderPage == 1) return;
    runningOrderPage--;
    runningOrderList = List.empty();
    notifyListeners();
    refreshCurrentOrders(selectedStatusFilter);
  }

  void runningOrdersNext() {
    runningOrderPage++;
    runningOrderList = List.empty();
    notifyListeners();
    refreshCurrentOrders(selectedStatusFilter);
  }

  void setStartDate(DateTime newDateTime) {
    periodFilterStartDate = newDateTime;
    notifyListeners();
  }

  void setEndDate(DateTime newDateTime) {
    periodFilterEndDate = newDateTime;
    notifyListeners();
  }
}
