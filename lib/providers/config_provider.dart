// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:hero_kit/models/config_model.dart';
import 'package:hero_kit/providers/hero_client_config_provider.dart';
import 'package:hero_kit/services/logger.dart';
import 'package:hero_kit/utility/translator.dart';
import 'package:hero_store_branch_app/Locale/translation.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_store_branch_app/data/config_repo.dart';
import 'package:hero_store_branch_app/enums/app_phase.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hero_store_branch_app/providers/auth_provider.dart';

class ConfigProvider extends HeroClientConfigProvider with ChangeNotifier {
  AppPhase appPhase = AppPhase.loading;
  late final SharedPreferences sp;
  String _authToken = "";
  String _currentLocale = "ar";
  late ConfigModel config;
  bool connected = true;

  Future<void> initLevel() async {
    sp = await SharedPreferences.getInstance();
    _authToken = sp.getString("saved_auth_token") ?? "";
    _currentLocale = sp.getString("savedLocale") ?? "ar";
    Translator().init(dictionary, _currentLocale);
    Log("Saved AuthToken: $authToken");
    //TODO
    // await Firebase.initializeApp();
    // FirebaseMessaging.onMessage.listen(onForegroundMessage);
    // FirebaseMessaging.onBackgroundMessage(onBackgroundMessage);
    connectToServer();

    if (authToken.isNotEmpty) {
      String? error = await AuthProvider().getProfile();

      if (error == null) {
        //TODO subscribe to topics instead

        appPhase = AppPhase.loggedIn;
      } else {
        AuthProvider().logout();
        appPhase = AppPhase.loggedOut;
      }
    } else {
      appPhase = AppPhase.loggedOut;
    }
    notifyListeners();

    //check for error reports - upload if exist
  }

  Future<void> connectToServer() async {
    //get config from backend
    try {
      config = await ConfigRepo().getConfig();
      connected = true;
    } catch (e) {
      Log("Error getting config: $e");
      //if server is unreachable ping google to check connectivity
      connected = await ConfigRepo().fetchMinimal();
      config = ConfigModel.maintenance();
    }
    checkStatus();
  }

  void checkStatus() {
    if (!connected) {
      appPhase = AppPhase.connectivity;
    } else if (config.minAppVersion == -1 || config.maintenanceMode == true) {
      appPhase = AppPhase.maintenance;
    } else if (config.minAppVersion > appVersion) {
      appPhase = AppPhase.forceUpdate;
    }
    notifyListeners();
  }

  void changeLocale(String newLocale) {
    _currentLocale = newLocale;
    sp.setString("savedLocale", newLocale);
    notifyListeners();
  }

  void clearAuthToken() {
    sp.remove("saved_auth_token");
    appPhase = AppPhase.loggedOut;
    notifyListeners();
  }

  void updateAuthToken(String token) {
    _authToken = token;
    sp.setString("saved_auth_token", token);
    appPhase = token.isEmpty ? AppPhase.loggedOut : AppPhase.loggedIn;
    notifyListeners();
  }

  factory ConfigProvider() => _instance;
  static final ConfigProvider _instance = ConfigProvider._();
  ConfigProvider._() {
    initLevel();
  }

  @override
  String get authToken => _authToken;

  @override
  int? get cityId => null;

  @override
  String get currentLocale => _currentLocale;

  @override
  int? get zoneId => null;
}
