import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/data/change_password_repo.dart';

class ChangePasswordProvider with ChangeNotifier {
  final ChangePasswordRepo _repo = ChangePasswordRepo();
  
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;

  /// Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    _isLoading = true;
    _errorMessage = null;
    _successMessage = null;
    notifyListeners();

    try {
      final response = await _repo.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
        newPasswordConfirmation: newPasswordConfirmation,
      );
      
      if (response["result"]) {
        _successMessage = "تم تغيير كلمة المرور بنجاح";
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _errorMessage = response["body"]["message"] ?? "فشل في تغيير كلمة المرور";
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = "حدث خطأ أثناء تغيير كلمة المرور";
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// Clear messages
  void clearMessages() {
    _errorMessage = null;
    _successMessage = null;
    notifyListeners();
  }
}
