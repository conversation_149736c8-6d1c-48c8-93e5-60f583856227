import 'package:flutter/material.dart';
import 'package:hero_kit/enums/store_branch_status.dart';
import 'package:hero_kit/models/store_branch_work_day.dart';
import 'package:hero_store_branch_app/data/auth_repo.dart';
import 'package:hero_store_branch_app/data/store_branch_repo.dart';
import 'package:hero_store_branch_app/models/hero_store_branch.dart';
import 'package:hero_store_branch_app/providers/config_provider.dart';

class AuthProvider with ChangeNotifier {
  late HeroStoreBranch storeBranch;

  Future<void> updateWorkTimeDays(
    StoreBranchStatus newStatus,
    List<StoreBranchWorkDay> workDays,
  ) async {
    // Update branch status if it has changed
    if (storeBranch.status != newStatus) {
      await StoreBranchRepo().setStoreBranchStatus(newStatus.index);
    }

    // Update work schedule
    Map<String, dynamic> result =
        await StoreBranchRepo().updateWorkSchedule(workDays);

    if (result["result"]) {
      // Success - update local store branch with new work days
      storeBranch.workDays = workDays;
      notifyListeners();
    }

    // Refresh the profile data to get the latest server state
    await getProfile();
  }

  Future<String?> login(String email, String password) async {
    Map<String, dynamic> result =
        await AuthRepo().login(email: email, password: password);
    if (result["success"]) {
      return null;
    } else {
      return result["message"];
    }
  }

  Future<String?> verifyOtp(String email, String otp) async {
    Map<String, dynamic> result = await AuthRepo().verifyOtp(email, otp);
    if (result["success"]) {
      storeBranch =
          HeroStoreBranch.fromJson(result["body"]["data"]["store_branch"]);
      ConfigProvider().updateAuthToken(result["body"]["data"]["token"]);
      return null;
    } else {
      return result["message"];
    }
  }

  Future<String?> getProfile() async {
    Map<String, dynamic> result = await StoreBranchRepo().getProfile();
    if (!result["result"]) {
      return "${result["body"]["message"]}";
    }
    storeBranch =
        HeroStoreBranch.fromJson(result["body"]["data"]["store_branch"]);
    notifyListeners();
    return null;
  }

  logout() {
    storeBranch = HeroStoreBranch.dummy();
    ConfigProvider().clearAuthToken();
    notifyListeners();
  }

  toggleIsTemporaryClosed() async {
    storeBranch.status = storeBranch.status.index > 0
        ? StoreBranchStatus.closed
        : StoreBranchStatus.open;
    notifyListeners();
    await StoreBranchRepo().setStoreBranchStatus(storeBranch.status.index);
    getProfile();
  }

  factory AuthProvider() => _instance;
  static final AuthProvider _instance = AuthProvider._();
  AuthProvider._();
}
