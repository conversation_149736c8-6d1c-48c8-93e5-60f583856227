import 'package:flutter/material.dart';
import 'package:hero_kit/enums/store_branch_status.dart';
import 'package:hero_kit/models/store_branch_work_day.dart';
import 'package:hero_store_branch_app/providers/auth_provider.dart';

class StoreBranchWorkTimeProvider with ChangeNotifier {
  StoreBranchStatus currentStatus;
  List<StoreBranchWorkDay> workDays;

  StoreBranchWorkTimeProvider({
    required this.currentStatus,
    required this.workDays,
  });
  factory StoreBranchWorkTimeProvider.from(
      {required AuthProvider authProvider}) {
    return StoreBranchWorkTimeProvider(
      currentStatus: authProvider.storeBranch.status,
      workDays: List.from(authProvider.storeBranch.workDays ?? []),
    );
  }
  setRestaurantStatus(StoreBranchStatus newStatus) {
    currentStatus = newStatus;
    notifyListeners();
  }

  void saveChanges() async {
    AuthProvider().updateWorkTimeDays(currentStatus, workDays);
  }

  setWorking(int dayIndex, bool newState) {
    int index = workDays.indexWhere((wd) => wd.day == dayIndex);
    if (index != -1) {
      workDays[index].working = newState;
      notifyListeners();
    }
  }

  void setStartTime(TimeOfDay newStartTime, int dayIndex) {
    int index = workDays.indexWhere((wd) => wd.day == dayIndex);
    if (index != -1) {
      workDays[index].openingTime = newStartTime;
      notifyListeners();
    }
  }

  void setEndTime(TimeOfDay newEndTime, int dayIndex) {
    int index = workDays.indexWhere((wd) => wd.day == dayIndex);
    if (index != -1) {
      workDays[index].closingTime = newEndTime;
      notifyListeners();
    }
  }
}
