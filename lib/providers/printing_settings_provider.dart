import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/data/printing_settings_repo.dart';
import 'package:hero_store_branch_app/models/printing_settings_model.dart';

class PrintingSettingsProvider with ChangeNotifier {
  final PrintingSettingsRepo _repo = PrintingSettingsRepo();
  
  PrintingSettingsModel? _settings;
  bool _isLoading = false;
  String? _errorMessage;

  PrintingSettingsModel? get settings => _settings;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  /// Load printing settings from API
  Future<void> loadPrintingSettings() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final response = await _repo.getPrintingSettings();
      
      if (response["result"]) {
        final data = response["body"]["data"];
        _settings = PrintingSettingsModel.fromJson(data);
      } else {
        _errorMessage = response["body"]["message"] ?? "Failed to load settings";
      }
    } catch (e) {
      _errorMessage = e.toString();
    }

    _isLoading = false;
    notifyListeners();
  }

  /// Update printing settings
  Future<bool> updatePrintingSettings({
    String? printMobileNumber,
    String? printFontSize,
  }) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final updatedSettings = PrintingSettingsModel(
        printMobileNumber: printMobileNumber,
        printFontSize: printFontSize,
      );

      final response = await _repo.updatePrintingSettings(updatedSettings);
      
      if (response["result"]) {
        // Update local settings
        _settings = _settings?.copyWith(
          printMobileNumber: printMobileNumber,
          printFontSize: printFontSize,
        ) ?? updatedSettings;
        
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _errorMessage = response["body"]["message"] ?? "Failed to update settings";
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// Get current phone number
  String get currentPhoneNumber => _settings?.printMobileNumber ?? "";

  /// Get current font size
  String get currentFontSize => _settings?.printFontSize ?? "medium";

  /// Get font size display text
  String getFontSizeDisplayText(String fontSize) {
    switch (fontSize) {
      case "small":
        return "صغير";
      case "medium":
        return "متوسط";
      case "large":
        return "كبير";
      default:
        return "متوسط";
    }
  }

  /// Get font size options
  List<Map<String, String>> get fontSizeOptions => [
    {"value": "small", "display": "صغير"},
    {"value": "medium", "display": "متوسط"},
    {"value": "large", "display": "كبير"},
  ];
}
