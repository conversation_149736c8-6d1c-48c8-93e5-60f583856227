import 'dart:convert';

import 'package:hero_kit/data/http_client.dart';
import 'package:hero_store_branch_app/data/urls.dart';

class AuthRepo {
  Future<Map<String, dynamic>> login({required String email, required String password}) async {
    Map<String, dynamic> response = await <PERSON><PERSON>lient().post(
      body: {"email": email, "password": password},
      path: Urls.authLoginUrl,
    );

    if (response["statusCode"] == 200) {
      return {
        "success": true,
        "body": jsonDecode(response["body"]),
      };
    }
    return {
      "success": false,
      "reasonPhrase": response["reasonPhrase"],
      "message": jsonDecode(response["body"])["message"],
    };
  }

  Future<Map<String, dynamic>> verifyOtp(String email, String otp) async {
    Map<String, dynamic> verifyOtpResponse = await HeroClient().post(
      path: Urls.verifyOtpUrl,
      body: {
        "email": email,
        "otp": otp,
      },
    );
    if (verifyOtpResponse["statusCode"] == 200) {
      return {"success": true, "body": jsonDecode(verifyOtpResponse["body"])};
    } else {
      return {"success": false, "message": "Something went wrong!"};
    }
  }

  Future<Map<String, dynamic>> resendOtp(String email) async {
    Map<String, dynamic> response = await HeroClient().post(
      body: {"email": email},
      path: Urls.resendOtpUrl,
    );

    if (response["statusCode"] == 200) {
      return {
        "success": true,
        "body": jsonDecode(response["body"]),
      };
    }
    return {
      "success": false,
      "reasonPhrase": response["reasonPhrase"],
      "message": jsonDecode(response["body"])["message"],
    };
  }
}
