import 'dart:convert';

import 'package:hero_kit/data/http_client.dart';
import 'package:hero_kit/enums/order_status.dart';
import 'package:hero_store_branch_app/data/urls.dart';

class OrderRepo {
  Future<Map<String, dynamic>> getCurrentOrders(String orderStatus,
      {int offset = 0, int count = 10, String stage = 'pending'}) async {
    Map<String, dynamic> response = await HeroClient().get(
      path: Urls.ongoingOrdersUrl,
      query:
          "?offset=$offset&count=$count&statuses_result=$orderStatus&stage=$stage",
    );
    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      Map<String, dynamic> body = jsonDecode(response["body"]);
      return {"result": true, "body": body};
    }
    return {
      "result": false,
      "reasonPhase": response["reasonPhase"],
      "body": response["body"]
    };
  }

  Future<Map<String, dynamic>> getCompleteOrders([String type = "all"]) async {
    Map<String, dynamic> response = await <PERSON><PERSON><PERSON>().get(
      path: Urls.completedOrdersUrl,
      query: "?offset=0&count=10&type=$type",
    );
    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      Map<String, dynamic> body = jsonDecode(response["body"]);
      return {"result": true, "body": body};
    }
    return {
      "result": false,
      "reasonPhase": response["reasonPhase"],
      "body": response["body"]
    };
  }

  Future<Map<String, dynamic>> getOrderDetails({required int orderID}) async {
    Map<String, dynamic> response =
        await HeroClient().get(path: Urls.deliveryOrder, query: "/$orderID");
    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      Map<String, dynamic> body = jsonDecode(response["body"]);
      return {"result": true, "body": body};
    }
    return {
      "result": false,
      "reasonPhase": response["reasonPhase"],
      "body": jsonDecode(response["body"])
    };
  }

  Future<Map<String, dynamic>> updateOrderStatus({
    required int orderID,
    required OrderStatus newOrderStatus,
    int? processingTime,
    String? reason,
  }) async {
    Map<String, dynamic> body = {
      "status": newOrderStatus.name,
    };
    if (processingTime != null) {
      body.addAll({"processing_time": "$processingTime"});
    }
    if (reason != null) {
      body.addAll({"reason": reason});
    }
    Map<String, dynamic> response = await HeroClient().put(
      path: Urls.deliveryOrder,
      query: "/$orderID",
      body: body,
    );

    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      Map<String, dynamic> body = jsonDecode(response["body"]);
      return {"result": true, "body": body};
    }
    return {
      "result": false,
      "reasonPhase": response["reasonPhase"],
      "body": jsonDecode(response["body"])
    };
  }

  Future<Map<String, dynamic>> getstatistics() async {
    Map<String, dynamic> response =
        await HeroClient().get(path: Urls.statistics);
    if (response["statusCode"] == 200) {
      Map<String, dynamic> body = jsonDecode(response["body"]);
      return {"result": true, "body": body};
    }
    return {
      "result": false,
      "reasonPhase": response["reasonPhase"],
      "body": response["body"]
    };
  }
}
