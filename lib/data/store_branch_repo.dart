import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:hero_kit/data/http_client.dart';
import 'package:hero_kit/models/store_branch_work_day.dart';
import 'package:hero_store_branch_app/data/urls.dart';

class StoreBranchRepo {
  Future<Map<String, dynamic>> getProfile() async {
    Map<String, dynamic> response = await HeroClient().get(
      path: Urls.vendorProfile,
    );

    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      return {
        "result": true,
        "reasonPhrase": response["reasonPhrase"],
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": jsonDecode(response["body"])
    };
  }

  Future<Map<String, dynamic>> setStoreBranchStatus(int status) async {
    Map<String, dynamic> response = await HeroClient().put(
      body: {"status": status},
      path: Urls.updateBranchStatus,
    );
    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      return {
        "result": true,
        "reasonPhrase": response["reasonPhrase"],
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": response["body"]
    };
  }

  Future<Map<String, dynamic>> updateWorkSchedule(
      List<StoreBranchWorkDay> workDays) async {
    // Convert work days to the required API format
    List<Map<String, dynamic>> payload = [];

    // Days of the week in English (API requirement)
    const List<String> dayNames = [
      "sunday",
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday"
    ];

    // Sort work days by day index to ensure correct order
    workDays.sort((a, b) => a.day.compareTo(b.day));

    for (var workDay in workDays) {
      payload.add({
        "day": dayNames[workDay.day],
        "is_active": workDay.working,
        "from": workDay.working ? _formatTimeForApi(workDay.openingTime) : null,
        "to": workDay.working ? _formatTimeForApi(workDay.closingTime) : null,
      });
    }

    Map<String, dynamic> response = await HeroClient().put(
      body: {"payload": payload},
      path: Urls.updateWorkSchedule,
    );

    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      return {
        "result": true,
        "reasonPhrase": response["reasonPhrase"],
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": jsonDecode(response["body"])
    };
  }

  String _formatTimeForApi(TimeOfDay time) {
    return "${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}";
  }
}
