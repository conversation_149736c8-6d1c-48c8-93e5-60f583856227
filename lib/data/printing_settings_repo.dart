import 'dart:convert';

import 'package:hero_kit/data/http_client.dart';
import 'package:hero_store_branch_app/data/urls.dart';
import 'package:hero_store_branch_app/models/printing_settings_model.dart';

class PrintingSettingsRepo {
  /// Get printing settings
  Future<Map<String, dynamic>> getPrintingSettings() async {
    Map<String, dynamic> response = await HeroClient().get(
      path: Urls.printingSettings,
    );

    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      return {
        "result": true,
        "reasonPhrase": response["reasonPhrase"],
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": jsonDecode(response["body"])
    };
  }

  /// Update printing settings
  Future<Map<String, dynamic>> updatePrintingSettings(
      PrintingSettingsModel settings) async {
    Map<String, dynamic> response = await HeroClient().patch(
      path: Urls.printingSettings,
      body: settings.toJson(),
    );

    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      return {
        "result": true,
        "reasonPhrase": response["reasonPhrase"],
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": jsonDecode(response["body"])
    };
  }
}
