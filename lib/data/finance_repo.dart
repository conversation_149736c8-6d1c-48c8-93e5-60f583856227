import 'dart:convert';

import 'package:hero_kit/data/http_client.dart';
import 'package:hero_kit/models/bank_info_model.dart';
import 'package:hero_store_branch_app/data/urls.dart';
import 'package:hero_store_branch_app/models/financial_summary_model.dart';

class FinanceRepo {
  Future<Map<String, dynamic>> getFinancialSummaryReport({
    required String reportType,
    required String startDate,
    required String endDate,
  }) async {
    String queryString =
        "?report_type=$reportType&start_date=$startDate&end_date=$endDate";

    Map<String, dynamic> response = await HeroClient().get(
      path: Urls.financialSummaryReport,
      query: queryString,
    );

    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      if (body["status"] == true) {
        FinancialSummaryModel financialData =
            FinancialSummaryModel.fromJson(body["data"]);
        return {
          "result": true,
          "data": financialData,
          "reasonPhrase": response["reasonPhrase"]
        };
      }
      return {
        "result": false,
        "reasonPhrase": "Data not available",
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": response["body"]
    };
  }

  // Bank Account Management Methods

  /// Create a new bank account
  Future<Map<String, dynamic>> createBankAccount({
    required int bankId,
    required int bankBranchId,
    required String personName,
    required String accountNumber,
  }) async {
    Map<String, dynamic> requestBody = {
      "bank_id": bankId,
      "bank_branch_id": bankBranchId,
      "person_name": personName,
      "account_number": accountNumber,
    };

    Map<String, dynamic> response = await HeroClient().post(
      body: requestBody,
      path: Urls.storeBranchBank,
    );

    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      return {
        "result": true,
        "reasonPhrase": response["reasonPhrase"],
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": jsonDecode(response["body"])
    };
  }

  /// Update an existing bank account
  Future<Map<String, dynamic>> updateBankAccount({
    required int bankAccountId,
    required int bankId,
    required int bankBranchId,
    required String personName,
    required String accountNumber,
  }) async {
    Map<String, dynamic> requestBody = {
      "bank_id": bankId,
      "bank_branch_id": bankBranchId,
      "person_name": personName,
      "account_number": accountNumber,
    };

    Map<String, dynamic> response = await HeroClient().put(
      body: requestBody,
      path: "${Urls.storeBranchBank}/$bankAccountId",
    );

    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      return {
        "result": true,
        "reasonPhrase": response["reasonPhrase"],
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": jsonDecode(response["body"])
    };
  }

  /// Get a specific bank account by ID
  Future<Map<String, dynamic>> getBankAccount(int bankAccountId) async {
    Map<String, dynamic> response = await HeroClient().get(
      path: "${Urls.storeBranchBank}/$bankAccountId",
    );

    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      return {
        "result": true,
        "reasonPhrase": response["reasonPhrase"],
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": jsonDecode(response["body"])
    };
  }

  /// Get all bank accounts for the store branch
  Future<Map<String, dynamic>> getAllBankAccounts() async {
    Map<String, dynamic> response = await HeroClient().get(
      path: Urls.storeBranchBank,
    );

    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      return {
        "result": true,
        "reasonPhrase": response["reasonPhrase"],
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": jsonDecode(response["body"])
    };
  }

  /// Delete a bank account
  Future<Map<String, dynamic>> deleteBankAccount(int bankAccountId) async {
    Map<String, dynamic> response = await HeroClient().delete(
      path: "${Urls.storeBranchBank}/$bankAccountId",
    );

    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      return {
        "result": true,
        "reasonPhrase": response["reasonPhrase"],
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": jsonDecode(response["body"])
    };
  }

  /// Get bank branches for a specific bank
  Future<Map<String, dynamic>> getBankBranches(int bankId) async {
    Map<String, dynamic> response = await HeroClient().get(
      path: "${Urls.getBankBranches}/$bankId",
    );

    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      return {
        "result": true,
        "reasonPhrase": response["reasonPhrase"],
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": jsonDecode(response["body"])
    };
  }

  /// Legacy method - kept for backward compatibility
  @Deprecated(
      'Use createBankAccount instead. This method will be removed in a future version.')
  Future<Map<String, dynamic>> addBankAccount(BankInfoModel bankInfo) async {
    Map<String, dynamic> response = await HeroClient().put(
      body: bankInfo.toJson(),
      path: Urls.vendorUpdateBankInfo,
    );
    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      return {
        "result": true,
        "reasonPhrase": response["reasonPhrase"],
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": response["body"]
    };
  }
}
