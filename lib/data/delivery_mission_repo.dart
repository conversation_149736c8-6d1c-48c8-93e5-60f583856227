import 'dart:convert';

import 'package:hero_kit/data/http_client.dart';
import 'package:hero_store_branch_app/data/urls.dart';

class DeliveryMissionRepo {
  Future<Map<String, dynamic>> fetchDeliveryFee(int zoneId) async {
    Map<String, dynamic> response = await HeroClient().get(
      path: Urls.deliveryMissionFee,
      query: "?customer_zone_id=$zoneId",
    );

    if (response["statusCode"] == 200) {
      Map<String, dynamic> body = jsonDecode(response["body"]);
      return {"result": true, "reasonPhrase": response["reasonPhrase"], "body": body};
    }
    return {"result": false, "reasonPhrase": response["reasonPhrase"], "body": response["body"]};
  }

  Future<Map<String, dynamic>> storeDeliveryMission({
    required String customerName,
    required String mobile,
    required String address,
    required String preparingTime,
    required int zoneId,
    required int orderAmount,
    int? neighborhoodId,
  }) async {
    Map<String, dynamic> response = await HeroClient().post(
      body: {
        "customer_name": customerName,
        "customer_mobile": mobile,
        "customer_address": address,
        "customer_zone_id": zoneId,
        if (neighborhoodId != null) "customer_neighborhood_id": neighborhoodId,
        "order_amount": orderAmount,
        "preparing_time": preparingTime,
      },
      path: Urls.requestDeliveryMan,
    );
    if (response["statusCode"] == 200) {
      Map<String, dynamic> body = jsonDecode(response["body"]);
      return {"result": true, "reasonPhrase": response["reasonPhrase"], "body": body};
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": jsonDecode(response["body"])
    };
  }
}
