import 'dart:convert';

import 'package:hero_kit/data/http_client.dart';
import 'package:hero_kit/models/config_model.dart';
import 'package:hero_kit/services/logger.dart';
import 'package:hero_store_branch_app/data/urls.dart';

class ConfigRepo {
  Future<ConfigModel> getConfig() async {
    Map<String, dynamic> configResponse = await HeroClient().get(path: Urls.configUrl);
    // no internet connection
    if (configResponse["statusCode"] == 0) {
      throw Exception("Failed to fetch config");
    }
    // server error
    if (configResponse["statusCode"] != 200) {
      return ConfigModel.maintenance();
    }
    late ConfigModel config;

    try {
      Map<String, dynamic> jsonMap = jsonDecode(configResponse["body"])["data"];
      config = ConfigModel.fromMap(jsonMap);
    } catch (e, s) {
      Log("Error parsing config! $e, $s");
      return ConfigModel.maintenance();
    }
    return config;
  }

  Future<bool> fetchMinimal() async {
    return await HeroClient().fetchMinimal();
  }
}
