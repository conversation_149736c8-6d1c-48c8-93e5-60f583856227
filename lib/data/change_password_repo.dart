import 'dart:convert';

import 'package:hero_kit/data/http_client.dart';
import 'package:hero_store_branch_app/data/urls.dart';

class ChangePasswordRepo {
  /// Change password
  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    Map<String, dynamic> response = await HeroClient().post(
      path: Urls.changePassword,
      body: {
        "current_password": currentPassword,
        "new_password": newPassword,
        "new_password_confirmation": newPasswordConfirmation,
      },
    );

    if (response["statusCode"] >= 200 && response["statusCode"] < 300) {
      var body = jsonDecode(response["body"]);
      return {
        "result": true,
        "reasonPhrase": response["reasonPhrase"],
        "body": body
      };
    }
    return {
      "result": false,
      "reasonPhrase": response["reasonPhrase"],
      "body": jsonDecode(response["body"])
    };
  }
}
