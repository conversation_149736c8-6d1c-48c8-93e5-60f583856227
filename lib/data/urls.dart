import 'package:hero_kit/data/base_urls.dart';

class Urls extends BaseUrls {
  static const String configUrl = "/api/store-branch/v1/configs";

  static const String authLoginUrl = "/api/store-branch/v1/auth/login";
  static const String verifyOtpUrl = "/api/store-branch/v1/auth/verify-otp";
  static const String resendOtpUrl = "/api/store-branch/v1/auth/resend-otp";
  static const String vendorProfile = "/api/store-branch/v1/auth/profile";

  static const String statistics = "/api/store-branch/v1/statistics";
  static const String financialSummaryReport =
      "/api/store-branch/v1/financial-summary-report";
  static const String ongoingOrdersUrl = "/api/store-branch/v1/orders";
  static const String completedOrdersUrl =
      "/api/store-branch/v1/general-filter";
  static const String updateBranchStatus =
      '/api/store-branch/v1/store-branch-status';
  static const String updateWorkSchedule =
      '/api/store-branch/v1/store-branch-schedule';
  static const String storeBranchBank =
      "/api/store-branch/v1/store-branch-bank";
  static const String getBankBranches =
      "/api/store-branch/v1/get-bank-branches";
  static const String deliveryOrder = "/api/store-branch/v1/order";

  static const String deliveryMissionFee =
      "/api/store-branch/v1/delivery-mission/fees";
  static const String requestDeliveryMan =
      "/api/store-branch/v1/delivery-mission/store";

  static const String printingSettings =
      "/api/store-branch/v1/printing-settings";
  static const String changePassword =
      "/api/store-branch/v1/auth/change-password";

  static const String vendorUpdateBankInfo = "/api/v1/vendor/update-bank-info";
  static const String vendorRequestWithdraw = "/api/v1/vendor/request-withdraw";

  static const String restaurantavatarPath = "/storage/restaurant";
  static const String productImagePath = "/storage/product";
  static const String deliveryManAvatarPath = "/storage/delivery-man";
  static const String bannerImagePath = "/storage/banner";
}
