import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:hero_store_branch_app/Locale/translation.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/consts.dart';
import 'package:hero_store_branch_app/providers/config_provider.dart';
import 'package:hero_store_branch_app/state_manager.dart';

class OtlubVendorApp extends StatelessWidget {
  const OtlubVendorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MediaQuery(
      data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
      child: MaterialApp(
        scrollBehavior: ScrollBehavior().copyWith(overscroll: false),
        debugShowCheckedModeBanner: false,
        navigatorKey: navigatorKey,
        title: 'Hero Partner',
        locale: Locale(Provider.of<ConfigProvider>(context).currentLocale),
        supportedLocales: dictionary.keys.map((e) => Locale(e)),
        localizationsDelegates: GlobalMaterialLocalizations.delegates,
        localeResolutionCallback: (locale, supportedLocales) {
          // Fallback to a default locale if the device's locale is not supported
          return supportedLocales.contains(locale) ? locale : Locale('en');
        },
        theme: ThemeData(
          fontFamily: "Psansa",
          colorScheme: ColorScheme.fromSeed(seedColor: primaryColor),
          useMaterial3: true,
        ),
        home: AppStateManager(),
      ),
    );
  }
}

final GlobalKey<NavigatorState> navigatorKey = GlobalKey();

extension NavigationExtension on GlobalKey {
  void pop<T extends Object?>([T? result]) {
    Navigator.of(currentContext!).pop(result);
  }

  Future<T?> push<T extends Object?>(Route<T> route) async {
    return await Navigator.of(currentContext!).push(route);
  }

  Future<T?> dialog<T>({required Widget Function(BuildContext) builder}) async {
    return await showDialog(context: currentContext!, builder: builder);
  }
}
