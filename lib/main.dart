import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/otlub_vendor_app.dart';
import 'package:hero_store_branch_app/providers/auth_provider.dart';
import 'package:hero_store_branch_app/providers/config_provider.dart';
import 'package:hero_store_branch_app/providers/notification_provider.dart';
import 'package:hero_store_branch_app/providers/orders_provider.dart';
import 'package:hero_store_branch_app/providers/finance_provider.dart';
import 'package:hero_store_branch_app/providers/printing_settings_provider.dart';

void main() {
  runApp(MultiProvider(
    providers: [
      ChangeNotifierProvider(create: (_) => AuthProvider()),
      ChangeNotifierProvider(create: (_) => ConfigProvider()),
      ChangeNotifierProvider(create: (_) => OrdersProvider()),
      ChangeNotifierProvider<NotificationProvider>(
          create: (context) => NotificationProvider()),
      ChangeNotifierProvider(create: (_) => FinanceProvider()),
      ChangeNotifierProvider(create: (_) => PrintingSettingsProvider()),
    ],
    child: OtlubVendorApp(),
  ));
}
