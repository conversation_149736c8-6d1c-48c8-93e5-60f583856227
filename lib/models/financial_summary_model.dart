class FinancialSummaryModel {
  final String totalAmount;
  final String cash;
  final String creditCard;
  final String ordersNumber;
  final String discountedAmount;
  final List<OrderSummary> orders;
  final String reportType;
  final String startDate;
  final String endDate;
  final String invoicePdfUrl;

  FinancialSummaryModel({
    required this.totalAmount,
    required this.cash,
    required this.creditCard,
    required this.ordersNumber,
    required this.discountedAmount,
    required this.orders,
    required this.reportType,
    required this.startDate,
    required this.endDate,
    required this.invoicePdfUrl,
  });

  factory FinancialSummaryModel.fromJson(Map<String, dynamic> json) {
    return FinancialSummaryModel(
      totalAmount: json['total_amount'] ?? '0₪',
      cash: json['cash'] ?? '0₪',
      creditCard: json['credit_card'] ?? '0₪',
      ordersNumber: json['orders_number'] ?? '0',
      discountedAmount: json['discounted_amount'] ?? '0₪',
      orders: (json['orders'] as List<dynamic>?)
              ?.map((order) => OrderSummary.fromJson(order))
              .toList() ??
          [],
      reportType: json['report_type'] ?? 'Daily',
      startDate: json['start_date'] ?? '',
      endDate: json['end_date'] ?? '',
      invoicePdfUrl: json['invoice_pdf_url'] ?? '',
    );
  }
}

class OrderSummary {
  final String id;
  final String title;
  final String subtitle;
  final String image;

  OrderSummary({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.image,
  });

  factory OrderSummary.fromJson(Map<String, dynamic> json) {
    return OrderSummary(
      id: json['id']?.toString() ?? '',
      title: json['title'] ?? '',
      subtitle: json['subtitle'] ?? '',
      image: json['image'] ?? '',
    );
  }
}
