import 'package:hero_kit/enums/store_branch_status.dart';
import 'package:hero_kit/models/food_group.dart';
import 'package:hero_kit/models/market_classification.dart';
import 'package:hero_kit/models/store.dart';
import 'package:hero_kit/models/store_branch.dart';
import 'package:hero_kit/models/bank_info_model.dart';
import 'package:hero_kit/models/store_branch_work_day.dart';
import 'package:hero_store_branch_app/providers/config_provider.dart';

class HeroStoreBranch extends StoreBranch {
  List<StoreBranchWorkDay>? workDays;
  BankInfoModel? bankInfo;

  HeroStoreBranch({
    required super.id,
    required super.name,
    required super.latitude,
    required super.longitude,
    required super.status,
    required super.tax,
    required super.deliveryTimeMin,
    required super.deliveryTimeMax,
    required super.deliveryTimeUnit,
    required super.businessModel,
    required super.description,
    required super.rating,
    required super.ratingCount,
    required super.workingScheduleFrom,
    required super.workingScheduleTo,
    required super.address,
    required super.store,
    super.foodGroups,
    super.marketClassifications,
    super.zoneID,
    super.discountType = "none",
    super.discount = 0,
    this.workDays,
    this.bankInfo,
    required super.config,
  });

  factory HeroStoreBranch.fromJson(Map<String, dynamic> json) {
    return HeroStoreBranch(
      id: json["id"],
      name: json["name"][ConfigProvider().currentLocale],
      zoneID: json["zone_id"],
      latitude: double.parse("${json["latitude"]}"),
      longitude: double.parse("${json["longitude"]}"),
      status: StoreBranchStatus.values.byName(json["status"] ?? "closed"),
      tax: double.parse("${json["addition_value_tax"]}"),
      deliveryTimeMin: json["delivery_time_min"],
      deliveryTimeMax: json["delivery_time_max"],
      deliveryTimeUnit: json["delivery_time_type"],
      businessModel: json["business_model"],
      description: json["description"][ConfigProvider().currentLocale],
      rating: double.tryParse("${json["rating"]}") ?? 0,
      ratingCount: int.tryParse("${json["rating_count"]}") ?? 0,
      workingScheduleFrom: json["work_schedule_from"] ?? "",
      workingScheduleTo: json["work_schedule_to"] ?? "",
      address: json["address"][ConfigProvider().currentLocale],
      discount: json["discount"],
      discountType: json["discount_type"] ?? 'none',
      store: Store.fromMap(
        json["store"] ??
            {
              "id": 2,
              "store_logo":
                  "http://hero.delivery/storage/uploads/store/NDE4eHh7QrPMa2vJbheLHjDpHnLI2ffaAGUrLVIW.png",
              "store_background":
                  "http://hero.delivery/storage/uploads/store/DnPmPUnG5bMaNnpZDLiXH4NvStzqujOyeC1rDFMB.png",
              "name": "Hero Burger",
              "description": "Hero Burger",
              "status": ""
            },
      ),
      foodGroups: json["food_groups"] == null
          ? null
          : List<FoodGroup>.from(
              (json["food_groups"] as List).map((l) => FoodGroup.fromMap(l))),
      marketClassifications: json["market_classifications"] == null
          ? null
          : List<MarketClassification>.from(
              (json["market_classifications"] as List)
                  .map((p) => MarketClassification.fromMap(p))),
      config: null,
    );
  }

  factory HeroStoreBranch.dummy() => HeroStoreBranch(
        id: -1,
        zoneID: 0,
        latitude: 0,
        longitude: 0,
        tax: 0,
        deliveryTimeMin: 0,
        deliveryTimeMax: 0,
        deliveryTimeUnit: "deliveryTimeUnit",
        businessModel: "businessModel",
        name: "not_found_or_removed",
        description: "description",
        rating: 0,
        ratingCount: -1,
        status: StoreBranchStatus.closed,
        workingScheduleFrom: "",
        workingScheduleTo: "",
        address: "address",
        store: Store.empty,
        foodGroups: [],
        marketClassifications: [],
        config: null,
      );
}

var sssssssssss = {
  "id": 2,
  "name": "Hero Burger",
  "zone_id": 4,
  "latitude": "31.82467400",
  "longitude": "35.22690400",
  "status": "open",
  "addition_value_tax": "18.00",
  "delivery_time_min": 20,
  "delivery_time_max": 50,
  "delivery_time_type": "minutes",
  "business_model": "delivery_and_application",
  "description": "the best burger",
  "address": "Bet Hanina",
  "work_schedule_from": "06:00",
  "work_schedule_to": "23:59",
  "discount_type": "percentage",
  "discount": 30,
  "rating": 4,
  "rating_count": 1,
  "store": {
    "id": 2,
    "store_logo":
        "http://hero.delivery/storage/uploads/store/NDE4eHh7QrPMa2vJbheLHjDpHnLI2ffaAGUrLVIW.png",
    "store_background":
        "http://hero.delivery/storage/uploads/store/DnPmPUnG5bMaNnpZDLiXH4NvStzqujOyeC1rDFMB.png",
    "name": "Hero Burger",
    "description": "Hero Burger",
    "status": ""
  },
  "food_groups": [
    {
      "food_classification_id": 4,
      "food_classification_id_name": "checken burger en",
      "foods": [
        {
          "id": 4,
          "store_id": 2,
          "store_id_name": "Hero Burger",
          "food_classification_id": 4,
          "food_classification_id_name": "checken burger en",
          "name": "Burger",
          "description": "Burger 150 gr",
          "image":
              "http://hero.delivery/storage/uploads/food/wclAqsCMROjRcRMEBFmGGNhNbCr4IjL3zacwM8vm.jpg",
          "food_code": "2006",
          "price": "50",
          "discount_type": null,
          "discount_value": 0,
          "max_cart_quantity": 9,
          "pivot": {
            "store_branch_id": 2,
            "food_id": 4,
            "price": "50.00",
            "attributes": null,
            "is_suggested": 1,
            "created_at": "2025-01-25T07:33:57.000000Z",
            "updated_at": "2025-03-11T17:36:32.000000Z"
          },
          "food_attributes": []
        },
        {
          "id": 20,
          "store_id": 2,
          "store_id_name": "Hero Burger",
          "food_classification_id": 4,
          "food_classification_id_name": "checken burger en",
          "name": "Bulldozer Burger",
          "description": "Bulldozer Burger",
          "image":
              "http://hero.delivery/storage/uploads/food/XvNZg1o2KWusQS40Miw0YnKc4n3nwxkwkoPrqwNs.png",
          "food_code": "2002",
          "price": "45",
          "discount_type": null,
          "discount_value": null,
          "max_cart_quantity": null,
          "pivot": {
            "store_branch_id": 2,
            "food_id": 20,
            "price": "45.00",
            "attributes": null,
            "is_suggested": null,
            "created_at": "2025-02-01T16:04:22.000000Z",
            "updated_at": "2025-03-11T17:36:32.000000Z"
          },
          "food_attributes": []
        },
        {
          "id": 51,
          "store_id": 2,
          "store_id_name": "Hero Burger",
          "food_classification_id": 4,
          "food_classification_id_name": "checken burger en",
          "name": "Burger",
          "description": "Burger",
          "image":
              "http://hero.delivery/storage/uploads/food/UQcUixySRTzl7DmW5DCEbtJ0dDNJoQkWNwcwPbRt.png",
          "food_code": "23546",
          "price": "40",
          "discount_type": "percentage",
          "discount_value": 30,
          "max_cart_quantity": null,
          "pivot": {
            "store_branch_id": 2,
            "food_id": 51,
            "price": "40.00",
            "attributes": null,
            "is_suggested": null,
            "created_at": "2025-02-01T16:04:22.000000Z",
            "updated_at": "2025-03-11T17:36:32.000000Z"
          },
          "food_attributes": []
        },
        {
          "id": 91,
          "store_id": 2,
          "store_id_name": "Hero Burger",
          "food_classification_id": 4,
          "food_classification_id_name": "checken burger en",
          "name": "Burger",
          "description": "Burger",
          "image":
              "http://hero.delivery/storage/uploads/food/rgmeZEdDel0N1ICgf805ayr1UfDqp6qXTEXbcL0j.png",
          "food_code": "3245432",
          "price": "34",
          "discount_type": null,
          "discount_value": null,
          "max_cart_quantity": null,
          "pivot": {
            "store_branch_id": 2,
            "food_id": 91,
            "price": "34.00",
            "attributes": null,
            "is_suggested": null,
            "created_at": "2025-02-21T10:20:06.000000Z",
            "updated_at": "2025-03-11T17:36:32.000000Z"
          },
          "food_attributes": []
        },
        {
          "id": 675,
          "store_id": 2,
          "store_id_name": "Hero Burger",
          "food_classification_id": 4,
          "food_classification_id_name": "checken burger en",
          "name": "Schnitzel",
          "description": "Schnitzel",
          "image":
              "http://hero.delivery/storage/uploads/food/9Hk4DiTOdkqc2IjqhEQ6SELeJSTVdlyBk8K9rTjV.png",
          "food_code": "٤٥٣٤٥",
          "price": "45",
          "discount_type": null,
          "discount_value": null,
          "max_cart_quantity": null,
          "pivot": {
            "store_branch_id": 2,
            "food_id": 675,
            "price": "45.00",
            "attributes": null,
            "is_suggested": 1,
            "created_at": "2025-03-11T17:36:32.000000Z",
            "updated_at": "2025-03-11T17:36:32.000000Z"
          },
          "food_attributes": [
            {
              "id": 211,
              "store_id": 2,
              "store_id_name": "هيرو برجر",
              "name": "Choose a drink",
              "is_required": 1,
              "min_no": 1,
              "max_no": 1,
              "choice_type": "single",
              "choice_type_text": "اختيار فردي",
              "food_attribute_choices": [
                {
                  "id": 1278,
                  "food_attribute_id": 211,
                  "food_attribute_id_name": "اختيار مشروب",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/ZkoJOuc9XOl9vIGxXHgNWYZlg2WVdcPIIWJJ8DZK.jpg",
                  "name": "Coca-Cola 330 ml",
                  "price": "6"
                },
                {
                  "id": 1279,
                  "food_attribute_id": 211,
                  "food_attribute_id_name": "اختيار مشروب",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/PS97Ztp8t9AqcGUoj46TfXbdjjPa1YETOpUPILqh.jpg",
                  "name": "Coca-Cola Zero 330 ml",
                  "price": "6"
                },
                {
                  "id": 1280,
                  "food_attribute_id": 211,
                  "food_attribute_id_name": "اختيار مشروب",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/8gLC90JuPjlZtQ3THafYnCjfBN97l79YD5ovgP49.jpg",
                  "name": "Sprite 330 ml",
                  "price": "6"
                },
                {
                  "id": 1281,
                  "food_attribute_id": 211,
                  "food_attribute_id_name": "اختيار مشروب",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/ptCHhqohC0Y5wy0skK8UmLFtL4QryhB8kLckOeq1.jpg",
                  "name": "Sprite Zero 330ml",
                  "price": "6"
                },
                {
                  "id": 1282,
                  "food_attribute_id": 211,
                  "food_attribute_id_name": "اختيار مشروب",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/5hLOVeebvJbqRVh5X6ShpECfV4tyRLW6E7tfoaSb.jpg",
                  "name": "Fanta 330 ml",
                  "price": "6"
                },
                {
                  "id": 1283,
                  "food_attribute_id": 211,
                  "food_attribute_id_name": "اختيار مشروب",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/Egu6ZiW9oFLenb46tNXCMwa04pCiE286Auw9UrT0.jpg",
                  "name": "Blu",
                  "price": "6"
                },
                {
                  "id": 1284,
                  "food_attribute_id": 211,
                  "food_attribute_id_name": "اختيار مشروب",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/1ghngvRS8lIGTF1w5WhkdRYNWvrYufpxoFqWObDy.jpg",
                  "name": "XL",
                  "price": "6"
                },
                {
                  "id": 1285,
                  "food_attribute_id": 211,
                  "food_attribute_id_name": "اختيار مشروب",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/A0BFsPWFTpbYP5Q0cHlX7LOptm1fqETj4MV8d4Fn.png",
                  "name": "Soda 250 ml",
                  "price": "6"
                },
                {
                  "id": 1286,
                  "food_attribute_id": 211,
                  "food_attribute_id_name": "اختيار مشروب",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/GjbrWGRmssIXcNTBFp2ijZERA6GfoqoBgDJYr2RX.jpg",
                  "name": "Strawberry banana juice 330 ml",
                  "price": "6"
                },
                {
                  "id": 1287,
                  "food_attribute_id": 211,
                  "food_attribute_id_name": "اختيار مشروب",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/l2x6jP4H45O3uqzAOcrx6LV7L8HRYyIyRcyouLYV.jpg",
                  "name": "Grape juice 330 ml",
                  "price": "6"
                }
              ]
            },
            {
              "id": 212,
              "store_id": 2,
              "store_id_name": "هيرو برجر",
              "name": "Add sauces",
              "is_required": 1,
              "min_no": 1,
              "max_no": 9,
              "choice_type": "multi",
              "choice_type_text": "اختيار متعدد",
              "food_attribute_choices": [
                {
                  "id": 1288,
                  "food_attribute_id": 212,
                  "food_attribute_id_name": "اضافة صوصات",
                  "image":
                      "http://hero.delivery/cms/assets/media/custom/default_no-image-available-1.png",
                  "name": "ketchup",
                  "price": "0"
                },
                {
                  "id": 1289,
                  "food_attribute_id": 212,
                  "food_attribute_id_name": "اضافة صوصات",
                  "image":
                      "http://hero.delivery/cms/assets/media/custom/default_no-image-available-1.png",
                  "name": "mayonnaise",
                  "price": "0"
                },
                {
                  "id": 1290,
                  "food_attribute_id": 212,
                  "food_attribute_id_name": "اضافة صوصات",
                  "image":
                      "http://hero.delivery/cms/assets/media/custom/default_no-image-available-1.png",
                  "name": "Sweet chili",
                  "price": "0"
                },
                {
                  "id": 1291,
                  "food_attribute_id": 212,
                  "food_attribute_id_name": "اضافة صوصات",
                  "image":
                      "http://hero.delivery/cms/assets/media/custom/default_no-image-available-1.png",
                  "name": "Hot chili",
                  "price": "0"
                },
                {
                  "id": 1292,
                  "food_attribute_id": 212,
                  "food_attribute_id_name": "اضافة صوصات",
                  "image":
                      "http://hero.delivery/cms/assets/media/custom/default_no-image-available-1.png",
                  "name": "Garlic sauce",
                  "price": "0"
                },
                {
                  "id": 1293,
                  "food_attribute_id": 212,
                  "food_attribute_id_name": "اضافة صوصات",
                  "image":
                      "http://hero.delivery/cms/assets/media/custom/default_no-image-available-1.png",
                  "name": "The Fayim",
                  "price": "0"
                },
                {
                  "id": 1294,
                  "food_attribute_id": 212,
                  "food_attribute_id_name": "اضافة صوصات",
                  "image":
                      "http://hero.delivery/cms/assets/media/custom/default_no-image-available-1.png",
                  "name": "Buffalo",
                  "price": "0"
                },
                {
                  "id": 1295,
                  "food_attribute_id": 212,
                  "food_attribute_id_name": "اضافة صوصات",
                  "image":
                      "http://hero.delivery/cms/assets/media/custom/default_no-image-available-1.png",
                  "name": "Barbecue",
                  "price": "0"
                },
                {
                  "id": 1296,
                  "food_attribute_id": 212,
                  "food_attribute_id_name": "اضافة صوصات",
                  "image":
                      "http://hero.delivery/cms/assets/media/custom/default_no-image-available-1.png",
                  "name": "mustard",
                  "price": "0"
                }
              ]
            },
            {
              "id": 213,
              "store_id": 2,
              "store_id_name": "هيرو برجر",
              "name": "Choose the type of bread",
              "is_required": 1,
              "min_no": 1,
              "max_no": 1,
              "choice_type": "single",
              "choice_type_text": "اختيار فردي",
              "food_attribute_choices": [
                {
                  "id": 1297,
                  "food_attribute_id": 213,
                  "food_attribute_id_name": "اختيار نوع الخبز",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/yHyvMFhfVNxbuPCNaTDwdWkHek9Osa87MICDltnv.jpg",
                  "name": "Bigla",
                  "price": "0"
                },
                {
                  "id": 1298,
                  "food_attribute_id": 213,
                  "food_attribute_id_name": "اختيار نوع الخبز",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/NgyqbAZEOGPowFDq5vHSSyGUUOAt8FHsielSdaEL.jpg",
                  "name": "Kamaj bread",
                  "price": "0"
                },
                {
                  "id": 1299,
                  "food_attribute_id": 213,
                  "food_attribute_id_name": "اختيار نوع الخبز",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/FCsqSKLDn443TlKVBZEhzPJy4RPI4NQyU7RIKQuC.jpg",
                  "name": "Jabeta bread",
                  "price": "0"
                }
              ]
            },
            {
              "id": 214,
              "store_id": 2,
              "store_id_name": "هيرو برجر",
              "name": "Grilled/Fried",
              "is_required": 1,
              "min_no": 1,
              "max_no": 1,
              "choice_type": "single",
              "choice_type_text": "اختيار فردي",
              "food_attribute_choices": [
                {
                  "id": 1300,
                  "food_attribute_id": 214,
                  "food_attribute_id_name": "مشوي / مقلي",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/55F9qKuYf7YzeiA31fb2Ewb7MOT5TzEPsA7BNaQZ.jpg",
                  "name": "Chips",
                  "price": "0"
                },
                {
                  "id": 1301,
                  "food_attribute_id": 214,
                  "food_attribute_id_name": "مشوي / مقلي",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/NlHiSuESqs4a6vOrJPPMf2bS1rUNpQu2RUdHhy3a.png",
                  "name": "Potato slices",
                  "price": "0"
                }
              ]
            },
            {
              "id": 215,
              "store_id": 2,
              "store_id_name": "هيرو برجر",
              "name": "Served with:",
              "is_required": 1,
              "min_no": 1,
              "max_no": 6,
              "choice_type": "multi",
              "choice_type_text": "اختيار متعدد",
              "food_attribute_choices": [
                {
                  "id": 1302,
                  "food_attribute_id": 215,
                  "food_attribute_id_name": "يقدم مع :",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/IdAitR4hPXO8d7vpMeVaWdvpBrDoq9QKeAJW5VSi.jpg",
                  "name": "Onion rings",
                  "price": "0"
                },
                {
                  "id": 1303,
                  "food_attribute_id": 215,
                  "food_attribute_id_name": "يقدم مع :",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/9NIGOnoaDExJcZIONFjflehzEEWDSJhFk9gD2mkd.jpg",
                  "name": "Cheddar cheese",
                  "price": "0"
                },
                {
                  "id": 1304,
                  "food_attribute_id": 215,
                  "food_attribute_id_name": "يقدم مع :",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/zM1BAE9DcL8BUborFmndo3xbWPdxVoq9ythO1M9I.jpg",
                  "name": "Motuzilla",
                  "price": "0"
                },
                {
                  "id": 1305,
                  "food_attribute_id": 215,
                  "food_attribute_id_name": "يقدم مع :",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/g9W0jePGHRDV5NuoLfG4FJ8Q9Qdj3TEZ7T4ZwNdO.jpg",
                  "name": "mushroom",
                  "price": "0"
                },
                {
                  "id": 1306,
                  "food_attribute_id": 215,
                  "food_attribute_id_name": "يقدم مع :",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/swuiV9MSngSgdbpOKakOKVDjVCyfzVsNisWZlqkI.jpg",
                  "name": "pineapple",
                  "price": "0"
                },
                {
                  "id": 1307,
                  "food_attribute_id": 215,
                  "food_attribute_id_name": "يقدم مع :",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/Ovc78PRwAdSu0K6luwaSd7Liw9IiC8kNv8xvpVnG.jpg",
                  "name": "onion",
                  "price": "0"
                }
              ]
            }
          ]
        }
      ]
    },
    {
      "food_classification_id": 26,
      "food_classification_id_name": "Beef burger",
      "foods": [
        {
          "id": 19,
          "store_id": 2,
          "store_id_name": "Hero Burger",
          "food_classification_id": 26,
          "food_classification_id_name": "Beef burger",
          "name": "Classic Burger 150 gm",
          "description": "Classic Burger 150 gm",
          "image":
              "http://hero.delivery/storage/uploads/food/beLM3uJolEnP0BlLSp3CEjeGEGHEp9zsBLLyzC9E.png",
          "food_code": "2001",
          "price": "45",
          "discount_type": null,
          "discount_value": null,
          "max_cart_quantity": null,
          "pivot": {
            "store_branch_id": 2,
            "food_id": 19,
            "price": "45.00",
            "attributes": null,
            "is_suggested": 1,
            "created_at": "2025-02-01T16:04:22.000000Z",
            "updated_at": "2025-03-11T17:36:32.000000Z"
          },
          "food_attributes": []
        },
        {
          "id": 22,
          "store_id": 2,
          "store_id_name": "Hero Burger",
          "food_classification_id": 26,
          "food_classification_id_name": "Beef burger",
          "name": "Classic Burger 300 gm",
          "description": "Classic Burger 300 gm",
          "image":
              "http://hero.delivery/storage/uploads/food/9RmvLH4swry4AFwdJhxfY24ldmO1eSeEx5NzTpXQ.png",
          "food_code": "2004",
          "price": "45",
          "discount_type": null,
          "discount_value": null,
          "max_cart_quantity": null,
          "pivot": {
            "store_branch_id": 2,
            "food_id": 22,
            "price": "45.00",
            "attributes": null,
            "is_suggested": null,
            "created_at": "2025-02-01T16:04:22.000000Z",
            "updated_at": "2025-03-11T17:36:32.000000Z"
          },
          "food_attributes": []
        },
        {
          "id": 93,
          "store_id": 2,
          "store_id_name": "Hero Burger",
          "food_classification_id": 26,
          "food_classification_id_name": "Beef burger",
          "name": "Hulk Burger",
          "description":
              "300 grams of Black Angus beef with the restaurant's special sauce",
          "image":
              "http://hero.delivery/storage/uploads/food/OtZ1TPDO8x8erdR9vDijWBCs8XhNu9Zta4xe5qXE.png",
          "food_code": "32135",
          "price": "62",
          "discount_type": null,
          "discount_value": null,
          "max_cart_quantity": null,
          "pivot": {
            "store_branch_id": 2,
            "food_id": 93,
            "price": "62.00",
            "attributes": null,
            "is_suggested": null,
            "created_at": "2025-02-21T07:09:05.000000Z",
            "updated_at": "2025-03-11T17:36:32.000000Z"
          },
          "food_attributes": [
            {
              "id": 88,
              "store_id": 2,
              "store_id_name": "هيرو برجر",
              "name": "Addons",
              "is_required": 1,
              "min_no": 1,
              "max_no": 3,
              "choice_type": "multi",
              "choice_type_text": "اختيار متعدد",
              "food_attribute_choices": [
                {
                  "id": 566,
                  "food_attribute_id": 88,
                  "food_attribute_id_name": "اضافات",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/WZ0ZRv0o4TOckdo28g0SyL7CJIltQiEOKBydpWHi.png",
                  "name": "Tomato",
                  "price": "0"
                },
                {
                  "id": 567,
                  "food_attribute_id": 88,
                  "food_attribute_id_name": "اضافات",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/cIOcFYr4t1heWDPMcvqJTax9iDxRpfD2VoRcB1R0.png",
                  "name": "Onion",
                  "price": "0"
                },
                {
                  "id": 568,
                  "food_attribute_id": 88,
                  "food_attribute_id_name": "اضافات",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/ITMgxAchHrzw2hXmu1sNu5FWHKdEQUwgJ28a7rsL.png",
                  "name": "pickled cucumber",
                  "price": "0"
                }
              ]
            },
            {
              "id": 89,
              "store_id": 2,
              "store_id_name": "هيرو برجر",
              "name": "More Extras",
              "is_required": 1,
              "min_no": 1,
              "max_no": 5,
              "choice_type": "multi",
              "choice_type_text": "اختيار متعدد",
              "food_attribute_choices": [
                {
                  "id": 569,
                  "food_attribute_id": 89,
                  "food_attribute_id_name": "اضافات اخرى",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/DxOosUehrp25FeJnQIAmeTRH5YkJwlvSItu2u4Oo.png",
                  "name": "egg",
                  "price": "5"
                },
                {
                  "id": 570,
                  "food_attribute_id": 89,
                  "food_attribute_id_name": "اضافات اخرى",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/ohAjPdgQbsp8F8gWIJCPaxmmX3ND7dnoanaNrnJ4.png",
                  "name": "avocado",
                  "price": "5"
                },
                {
                  "id": 571,
                  "food_attribute_id": 89,
                  "food_attribute_id_name": "اضافات اخرى",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/V3Zfb3LdLNWMkQeRZOZgjVC641MCCfQdcc1nGNaQ.png",
                  "name": "cheddar cheese",
                  "price": "5"
                },
                {
                  "id": 572,
                  "food_attribute_id": 89,
                  "food_attribute_id_name": "اضافات اخرى",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/1NdFkkl2EpPOBkB4QTzzhHhkZ2GtKH5kuXQONfis.png",
                  "name": "Caramelized onions",
                  "price": "3"
                },
                {
                  "id": 573,
                  "food_attribute_id": 89,
                  "food_attribute_id_name": "اضافات اخرى",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/8Qqw9o9c94edt6fosdAvHI40XapAnTPXCvelJrzQ.png",
                  "name": "mushroom",
                  "price": "5"
                }
              ]
            }
          ]
        }
      ]
    },
    {
      "food_classification_id": 27,
      "food_classification_id_name": "",
      "foods": [
        {
          "id": 21,
          "store_id": 2,
          "store_id_name": "Hero Burger",
          "food_classification_id": 27,
          "food_classification_id_name": "",
          "name": "Mana Burger 150 gm",
          "description": "Mana Burger 150 gm",
          "image":
              "http://hero.delivery/storage/uploads/food/VBsnmX6POZjjjV3Wm013ZkmhXAKXzoLF3A9E8IRH.png",
          "food_code": "2003",
          "price": "45",
          "discount_type": null,
          "discount_value": null,
          "max_cart_quantity": null,
          "pivot": {
            "store_branch_id": 2,
            "food_id": 21,
            "price": "45.00",
            "attributes": null,
            "is_suggested": null,
            "created_at": "2025-02-01T16:04:22.000000Z",
            "updated_at": "2025-03-11T17:36:32.000000Z"
          },
          "food_attributes": []
        },
        {
          "id": 14,
          "store_id": 2,
          "store_id_name": "Hero Burger",
          "food_classification_id": 27,
          "food_classification_id_name": "",
          "name": "برجر 150",
          "description": "برجر 150",
          "image":
              "http://hero.delivery/storage/uploads/food/W6UBEfGkqqiaClHUYJhqO2qVdWiyulfukKUAfIzZ.png",
          "food_code": "25412",
          "price": "50",
          "discount_type": null,
          "discount_value": null,
          "max_cart_quantity": null,
          "pivot": {
            "store_branch_id": 2,
            "food_id": 14,
            "price": "50.00",
            "attributes": null,
            "is_suggested": 1,
            "created_at": "2025-02-02T13:15:35.000000Z",
            "updated_at": "2025-03-11T17:36:32.000000Z"
          },
          "food_attributes": []
        },
        {
          "id": 94,
          "store_id": 2,
          "store_id_name": "Hero Burger",
          "food_classification_id": 27,
          "food_classification_id_name": "",
          "name": "Classic Burger Meal",
          "description": "Meal includes fries + drink",
          "image":
              "http://hero.delivery/storage/uploads/food/1q50XnT6pfS6LBpjB9pbd8YbWTdK07U2kypJOOh7.png",
          "food_code": "254",
          "price": "75",
          "discount_type": null,
          "discount_value": null,
          "max_cart_quantity": null,
          "pivot": {
            "store_branch_id": 2,
            "food_id": 94,
            "price": "75.00",
            "attributes": null,
            "is_suggested": null,
            "created_at": "2025-02-21T10:20:06.000000Z",
            "updated_at": "2025-03-11T17:36:32.000000Z"
          },
          "food_attributes": [
            {
              "id": 90,
              "store_id": 2,
              "store_id_name": "هيرو برجر",
              "name": "Addons",
              "is_required": 1,
              "min_no": 1,
              "max_no": 3,
              "choice_type": "multi",
              "choice_type_text": "اختيار متعدد",
              "food_attribute_choices": [
                {
                  "id": 574,
                  "food_attribute_id": 90,
                  "food_attribute_id_name": "اضافات",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/WZ0ZRv0o4TOckdo28g0SyL7CJIltQiEOKBydpWHi.png",
                  "name": "Tomato",
                  "price": "0"
                },
                {
                  "id": 575,
                  "food_attribute_id": 90,
                  "food_attribute_id_name": "اضافات",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/cIOcFYr4t1heWDPMcvqJTax9iDxRpfD2VoRcB1R0.png",
                  "name": "Onion",
                  "price": "0"
                },
                {
                  "id": 576,
                  "food_attribute_id": 90,
                  "food_attribute_id_name": "اضافات",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/ITMgxAchHrzw2hXmu1sNu5FWHKdEQUwgJ28a7rsL.png",
                  "name": "pickled cucumber",
                  "price": "0"
                }
              ]
            },
            {
              "id": 91,
              "store_id": 2,
              "store_id_name": "هيرو برجر",
              "name": "More Extras",
              "is_required": 1,
              "min_no": 1,
              "max_no": 5,
              "choice_type": "multi",
              "choice_type_text": "اختيار متعدد",
              "food_attribute_choices": [
                {
                  "id": 577,
                  "food_attribute_id": 91,
                  "food_attribute_id_name": "اضافات اخرى",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/DxOosUehrp25FeJnQIAmeTRH5YkJwlvSItu2u4Oo.png",
                  "name": "egg",
                  "price": "5"
                },
                {
                  "id": 578,
                  "food_attribute_id": 91,
                  "food_attribute_id_name": "اضافات اخرى",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/ohAjPdgQbsp8F8gWIJCPaxmmX3ND7dnoanaNrnJ4.png",
                  "name": "avocado",
                  "price": "5"
                },
                {
                  "id": 579,
                  "food_attribute_id": 91,
                  "food_attribute_id_name": "اضافات اخرى",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/V3Zfb3LdLNWMkQeRZOZgjVC641MCCfQdcc1nGNaQ.png",
                  "name": "cheddar cheese",
                  "price": "5"
                },
                {
                  "id": 580,
                  "food_attribute_id": 91,
                  "food_attribute_id_name": "اضافات اخرى",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/1NdFkkl2EpPOBkB4QTzzhHhkZ2GtKH5kuXQONfis.png",
                  "name": "Caramelized onions",
                  "price": "3"
                },
                {
                  "id": 581,
                  "food_attribute_id": 91,
                  "food_attribute_id_name": "اضافات اخرى",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/8Qqw9o9c94edt6fosdAvHI40XapAnTPXCvelJrzQ.png",
                  "name": "mushroom",
                  "price": "5"
                }
              ]
            },
            {
              "id": 92,
              "store_id": 2,
              "store_id_name": "هيرو برجر",
              "name": "Choose chips",
              "is_required": 1,
              "min_no": 1,
              "max_no": 1,
              "choice_type": "single",
              "choice_type_text": "اختيار فردي",
              "food_attribute_choices": [
                {
                  "id": 582,
                  "food_attribute_id": 92,
                  "food_attribute_id_name": "اختر شيبس",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/BYKeuzUgFG2O4qluIGaSIS1ql79wdYTKfi51ZPrR.jpg",
                  "name": "curly",
                  "price": "5"
                },
                {
                  "id": 583,
                  "food_attribute_id": 92,
                  "food_attribute_id_name": "اختر شيبس",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/HkWqlhJ3hwY0JrecmXr2ED1D6Bo4gtMxUXeBaDbR.jpg",
                  "name": "chips",
                  "price": "0"
                }
              ]
            },
            {
              "id": 97,
              "store_id": 2,
              "store_id_name": "هيرو برجر",
              "name": "اختر مشروب",
              "is_required": 1,
              "min_no": 1,
              "max_no": 1,
              "choice_type": "single",
              "choice_type_text": "اختيار فردي",
              "food_attribute_choices": [
                {
                  "id": 592,
                  "food_attribute_id": 97,
                  "food_attribute_id_name": "اختر مشروب",
                  "image":
                      "http://hero.delivery/storage/uploads/food_attribute_choice_images/BQD4ikPgFUuXOaAw31oB725mFZm8F5WiRIjkGe32.jpg",
                  "name": "كولا 330 ملم",
                  "price": "0"
                }
              ]
            }
          ]
        }
      ]
    },
    {
      "food_classification_id": 28,
      "food_classification_id_name": "",
      "foods": [
        {
          "id": 92,
          "store_id": 2,
          "store_id_name": "Hero Burger",
          "food_classification_id": 28,
          "food_classification_id_name": "",
          "name": "Pizza",
          "description": "Pizza",
          "image":
              "http://hero.delivery/storage/uploads/food/E7YuyAJrn5dieEBDbLmrOoJ65DtE04seiwczAbj8.png",
          "food_code": "203251",
          "price": "50",
          "discount_type": null,
          "discount_value": null,
          "max_cart_quantity": null,
          "pivot": {
            "store_branch_id": 2,
            "food_id": 92,
            "price": "50.00",
            "attributes": null,
            "is_suggested": null,
            "created_at": "2025-02-20T09:54:48.000000Z",
            "updated_at": "2025-03-11T17:36:32.000000Z"
          },
          "food_attributes": [
            {
              "id": 87,
              "store_id": 2,
              "store_id_name": "هيرو برجر",
              "name": "Addons",
              "is_required": 1,
              "min_no": 1,
              "max_no": 3,
              "choice_type": "multi",
              "choice_type_text": "اختيار متعدد",
              "food_attribute_choices": [
                {
                  "id": 563,
                  "food_attribute_id": 87,
                  "food_attribute_id_name": "اضافات",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/WZ0ZRv0o4TOckdo28g0SyL7CJIltQiEOKBydpWHi.png",
                  "name": "Tomato",
                  "price": "0"
                },
                {
                  "id": 564,
                  "food_attribute_id": 87,
                  "food_attribute_id_name": "اضافات",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/cIOcFYr4t1heWDPMcvqJTax9iDxRpfD2VoRcB1R0.png",
                  "name": "Onion",
                  "price": "0"
                },
                {
                  "id": 565,
                  "food_attribute_id": 87,
                  "food_attribute_id_name": "اضافات",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/ITMgxAchHrzw2hXmu1sNu5FWHKdEQUwgJ28a7rsL.png",
                  "name": "pickled cucumber",
                  "price": "0"
                }
              ]
            }
          ]
        },
        {
          "id": 191,
          "store_id": 2,
          "store_id_name": "Hero Burger",
          "food_classification_id": 28,
          "food_classification_id_name": "",
          "name": "Pizza 2",
          "description": "Pizza",
          "image":
              "http://hero.delivery/storage/uploads/food/8d9w9DojM05x9qEt9jesnMahKMS3OcU3mEFyCALo.png",
          "food_code": "2541",
          "price": "65",
          "discount_type": null,
          "discount_value": null,
          "max_cart_quantity": null,
          "pivot": {
            "store_branch_id": 2,
            "food_id": 191,
            "price": "65.00",
            "attributes": null,
            "is_suggested": null,
            "created_at": "2025-02-24T11:41:47.000000Z",
            "updated_at": "2025-03-11T17:36:32.000000Z"
          },
          "food_attributes": [
            {
              "id": 93,
              "store_id": 2,
              "store_id_name": "هيرو برجر",
              "name": "جبنة بارميزان",
              "is_required": 1,
              "min_no": 1,
              "max_no": 1,
              "choice_type": "multi",
              "choice_type_text": "اختيار متعدد",
              "food_attribute_choices": [
                {
                  "id": 584,
                  "food_attribute_id": 93,
                  "food_attribute_id_name": "جبنة بارميزان",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/Zhg4NMWanMg59Z9aIVt5BnlQpTpS1ylnvvrhCmGU.jpg",
                  "name": "كل البيتزا",
                  "price": "0"
                },
                {
                  "id": 585,
                  "food_attribute_id": 93,
                  "food_attribute_id_name": "جبنة بارميزان",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/jTg66kbCui3CjUo5ikHy9qhRjvWJNzoB1e2UBlST.jpg",
                  "name": "الجهة اليمين",
                  "price": "0"
                },
                {
                  "id": 586,
                  "food_attribute_id": 93,
                  "food_attribute_id_name": "جبنة بارميزان",
                  "image":
                      "http://hero.delivery/storage/food_attribute_choice_images/4VnYFYJaiwSyv3LytchW771VHXYD6X61iT6WDy9L.jpg",
                  "name": "الجهة الشمال",
                  "price": "0"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};
