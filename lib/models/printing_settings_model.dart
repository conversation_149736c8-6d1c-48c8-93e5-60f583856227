class PrintingSettingsModel {
  final int? id;
  final int? storeBranchId;
  final String? printMobileNumber;
  final String? printFontSize;
  final String? printLang;
  final String? createdAt;
  final String? updatedAt;

  PrintingSettingsModel({
    this.id,
    this.storeBranchId,
    this.printMobileNumber,
    this.printFontSize,
    this.printLang,
    this.createdAt,
    this.updatedAt,
  });

  factory PrintingSettingsModel.fromJson(Map<String, dynamic> json) {
    return PrintingSettingsModel(
      id: json['id'],
      storeBranchId: json['store_branch_id'],
      printMobileNumber: json['print_mobile_number'],
      printFontSize: json['print_font_size'],
      printLang: json['print_lang'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (printMobileNumber != null) data['print_mobile_number'] = printMobileNumber;
    if (printFontSize != null) data['print_font_size'] = printFontSize;
    return data;
  }

  PrintingSettingsModel copyWith({
    int? id,
    int? storeBranchId,
    String? printMobileNumber,
    String? printFontSize,
    String? printLang,
    String? createdAt,
    String? updatedAt,
  }) {
    return PrintingSettingsModel(
      id: id ?? this.id,
      storeBranchId: storeBranchId ?? this.storeBranchId,
      printMobileNumber: printMobileNumber ?? this.printMobileNumber,
      printFontSize: printFontSize ?? this.printFontSize,
      printLang: printLang ?? this.printLang,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
