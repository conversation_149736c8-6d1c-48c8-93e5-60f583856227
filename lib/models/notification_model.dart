import 'dart:math';

class NotificationModel {
  final int id;
  final String title;
  final String body;
  final String? image;
  bool read;
  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    this.image,
    this.read = false,
  });
  factory NotificationModel.dummy() {
    return NotificationModel(
      id: Random().nextInt(50),
      title: "نص تجريبي للأشعارات\nسطر جديد",
      body: "body",
    );
  }
}
