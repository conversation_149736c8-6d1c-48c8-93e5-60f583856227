// class Translation {
//   String of(String text, String locale) => dictionary[locale]?[text] ?? text;
// }

const Map<String, String> hebrew = {
  "start_date": "תאריך התחלה",
  "end_date": "תאריך סיום",
  "store_branch_details": "פרטי המסעדה",
  "restaurant_data": "פרטי המסעדה",
  "restaurant_working_hours": "שעות פעילות המסעדה",
  "restaurant_phone": "מספר טלפון",
  "language": "שפה",
  "display_mode": "מצב תצוגה",
  "printer_settings": "הגדרות מדפסת",
  "text_size": "גודל טקסט",
  "update": "עדכן",
  "working_hours_app": "שעות פעילות האפליקציה",
  "save_hours": "שמור",
  "cancel_action": "ביטול",
  "edit_working_hours": "עריכת שעות פעילות",
  "working": "פעיל",
  "closed": "סגור",
  "opening_time": "שעת פתיחה",
  "closing_time": "שעת סגירה",
  "changes_saved_successfully": "השינויים נשמרו בהצלחה",
  "bank_account_created_successfully": "החשבון הבנקאי נוצר בהצלחה",
  "bank_account_updated_successfully": "החשבון הבנקאי עודכן בהצלחה",
  "bank_account_deleted_successfully": "החשבון הבנקאי נמחק בהצלחה",
  "delete_bank_account": "מחק חשבון בנקאי",
  "delete_bank_account_confirmation":
      "האם אתה בטוח שברצונך למחוק את החשבון הבנקאי הזה?",
  "delete": "מחק",
  "cancel": "ביטול",
  "retry": "נסה שוב",
};
