// class Translation {
//   String of(String text, String locale) => dictionary[locale]?[text] ?? text;
// }

const Map<String, String> english = {
  "start_date": "Start Date",
  "end_date": "End Date",
  "store_branch_details": "Restaurant Details",
  "restaurant_data": "Restaurant Data",
  "restaurant_working_hours": "Restaurant Working Hours",
  "restaurant_phone": "Phone Number",
  "language": "Language",
  "display_mode": "Display Mode",
  "printer_settings": "Printer Settings",
  "text_size": "Text Size",
  "update": "Update",
  "working_hours_app": "Application Working Hours",
  "save_hours": "Save",
  "cancel_action": "Cancel",
  "edit_working_hours": "Edit Working Hours",
  "working": "Working",
  "closed": "Closed",
  "opening_time": "Opening Time",
  "closing_time": "Closing Time",
  "changes_saved_successfully": "Changes saved successfully",
  "bank_account_created_successfully": "Bank account created successfully",
  "bank_account_updated_successfully": "Bank account updated successfully",
  "bank_account_deleted_successfully": "Bank account deleted successfully",
  "delete_bank_account": "Delete Bank Account",
  "delete_bank_account_confirmation":
      "Are you sure you want to delete this bank account?",
  "delete": "Delete",
  "cancel": "Cancel",
  "retry": "Retry",
};
