import 'package:flutter/material.dart';
import 'package:hero_store_branch_app/enums/app_phase.dart';
import 'package:hero_store_branch_app/view/screens/connectivity_screen.dart';
import 'package:hero_store_branch_app/view/screens/maintenance_screen.dart';
import 'package:provider/provider.dart';
import 'package:hero_store_branch_app/providers/config_provider.dart';
import 'package:hero_store_branch_app/view/screens/auth/auth_screen.dart';
import 'package:hero_store_branch_app/view/screens/force_update_screen.dart';
import 'package:hero_store_branch_app/view/screens/main_screen/main_screen.dart';
import 'package:hero_store_branch_app/view/screens/splash_screen/splash_screen.dart';

class AppStateManager extends StatelessWidget {
  const AppStateManager({super.key});

  @override
  Widget build(BuildContext context) {
    return Selector<ConfigProvider, AppPhase>(
      builder: (context, appPhase, child) {
        return switch (appPhase) {
          AppPhase.maintenance => MaintenanceScreen(),
          AppPhase.connectivity => ConnectivityScreen(),
          AppPhase.forceUpdate => ForceUpdateScreen(),
          AppPhase.loading => SplashScreen(),
          AppPhase.loggedIn => MainScreen(),
          AppPhase.loggedOut => AuthScreen(),
        };
      },
      selector: (_, configProvider) => configProvider.appPhase,
    );
  }
}
